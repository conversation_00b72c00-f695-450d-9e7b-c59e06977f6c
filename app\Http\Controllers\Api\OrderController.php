<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OrderController extends Controller
{
    /**
     * Display a listing of orders for the authenticated user
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Get orders where user is either sender or receiver
        $orders = Order::where(function($query) use ($user) {
            $query->where('from_user_id', $user->id)
                  ->orWhere('to_user_id', $user->id);
        })
        ->with(['fromUser', 'toUser', 'blood', 'healthCenter'])
        ->orderBy('created_at', 'desc')
        ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $orders
        ]);
    }

    /**
     * Store a newly created order
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string',
            'to_user_id' => 'required|exists:users,id',
            'blood_id' => 'required|exists:blood,id',
            'health_center_id' => 'required|exists:health_center,id',
            'date' => 'sometimes|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if user is trying to send order to themselves
        if ($request->to_user_id == $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot send order to yourself'
            ], 400);
        }

        $order = Order::create([
            'message' => $request->message,
            'from_user_id' => $request->user()->id,
            'to_user_id' => $request->to_user_id,
            'blood_id' => $request->blood_id,
            'health_center_id' => $request->health_center_id,
            'date' => $request->date,
            'status' => 'pending',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order created successfully',
            'data' => $order->load(['fromUser', 'toUser', 'blood', 'healthCenter'])
        ], 201);
    }

    /**
     * Display the specified order
     */
    public function show(Request $request, Order $order)
    {
        $user = $request->user();

        // Check if user is authorized to view this order
        if ($order->from_user_id !== $user->id && $order->to_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this order'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $order->load(['fromUser', 'toUser', 'blood', 'healthCenter'])
        ]);
    }

    /**
     * Update the specified order status
     */
    public function update(Request $request, Order $order)
    {
        $user = $request->user();

        // Only the receiver can update the order status
        if ($order->to_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Only the order recipient can update the status'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,accepted,rejected,completed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $order->update([
            'status' => $request->status
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully',
            'data' => $order->load(['fromUser', 'toUser', 'blood', 'healthCenter'])
        ]);
    }

    /**
     * Remove the specified order
     */
    public function destroy(Request $request, Order $order)
    {
        $user = $request->user();

        // Only the sender can delete the order and only if it's pending
        if ($order->from_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Only the order sender can delete the order'
            ], 403);
        }

        if ($order->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Can only delete pending orders'
            ], 400);
        }

        $order->delete();

        return response()->json([
            'success' => true,
            'message' => 'Order deleted successfully'
        ]);
    }

    /**
     * Get orders sent by the authenticated user
     */
    public function sent(Request $request)
    {
        $orders = Order::where('from_user_id', $request->user()->id)
            ->with(['toUser', 'blood', 'healthCenter'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $orders
        ]);
    }

    /**
     * Get orders received by the authenticated user
     */
    public function received(Request $request)
    {
        $orders = Order::where('to_user_id', $request->user()->id)
            ->with(['fromUser', 'blood', 'healthCenter'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $orders
        ]);
    }
}
