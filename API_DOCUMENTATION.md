# Blood Bank API Documentation

## تم تفعيل API بنجاح مع Laravel Sanctum

### 🔐 المصادقة والأمان
- **Laravel Sanctum**: مثبت ومُفعل لإدارة API tokens
- **Middleware**: تم إعداد middleware للمصادقة والتحقق من صلاحيات Admin
- **User Model**: محدث بـ HasApiTokens trait

### 📋 API Endpoints

#### 🔓 Public Routes (لا تحتاج مصادقة)

```
POST   /api/register           - تسجيل مستخدم جديد
POST   /api/login              - تسجيل الدخول
GET    /api/blood-types        - قائمة أنواع الدم
GET    /api/states             - قائمة المحافظات
GET    /api/cities             - قائمة المدن
GET    /api/diseases           - قائمة الأمراض
GET    /api/report-reasons     - قائمة أسباب التقارير
```

#### 🔒 Protected Routes (تحتاج مصادقة)

##### Authentication Routes
```
POST   /api/logout             - تسجيل الخروج
GET    /api/profile            - عرض الملف الشخصي
PUT    /api/profile            - تحديث الملف الشخصي
GET    /api/user               - معلومات المستخدم الحالي
```

##### Blood Management
```
GET    /api/blood              - قائمة أنواع الدم (مع إدارة)
POST   /api/blood              - إضافة نوع دم جديد (Admin only)
GET    /api/blood/{id}         - عرض نوع دم محدد
PUT    /api/blood/{id}         - تحديث نوع دم (Admin only)
DELETE /api/blood/{id}         - حذف نوع دم (Admin only)
GET    /api/blood/{id}/donors  - قائمة المتبرعين بنوع دم محدد
```

##### State Management
```
GET    /api/states             - قائمة المحافظات
POST   /api/states             - إضافة محافظة جديدة (Admin only)
GET    /api/states/{id}        - عرض محافظة محددة
PUT    /api/states/{id}        - تحديث محافظة (Admin only)
DELETE /api/states/{id}        - حذف محافظة (Admin only)
GET    /api/states/{id}/cities - قائمة المدن في محافظة محددة
```

##### City Management
```
GET    /api/cities                        - قائمة المدن
POST   /api/cities                        - إضافة مدينة جديدة (Admin only)
GET    /api/cities/{id}                   - عرض مدينة محددة
PUT    /api/cities/{id}                   - تحديث مدينة (Admin only)
DELETE /api/cities/{id}                   - حذف مدينة (Admin only)
GET    /api/cities/{id}/health-centers    - قائمة المراكز الصحية في مدينة محددة
```

##### Health Center Management
```
GET    /api/health-centers                    - قائمة المراكز الصحية
POST   /api/health-centers                    - إضافة مركز صحي جديد (Admin only)
GET    /api/health-centers/{id}               - عرض مركز صحي محدد
PUT    /api/health-centers/{id}               - تحديث مركز صحي
DELETE /api/health-centers/{id}               - حذف مركز صحي (Admin only)
GET    /api/health-centers/managed/list       - المراكز الصحية المُدارة من المستخدم
GET    /api/health-centers/associated/list    - المراكز الصحية المرتبطة بالمستخدم
```

##### Disease Management
```
GET    /api/diseases                          - قائمة الأمراض
POST   /api/diseases                          - إضافة مرض جديد (Admin only)
GET    /api/diseases/{id}                     - عرض مرض محدد
PUT    /api/diseases/{id}                     - تحديث مرض (Admin only)
DELETE /api/diseases/{id}                     - حذف مرض (Admin only)
POST   /api/diseases/{id}/associate           - ربط مرض بالمستخدم الحالي
DELETE /api/diseases/{id}/disassociate        - إلغاء ربط مرض من المستخدم الحالي
GET    /api/user/diseases                     - قائمة أمراض المستخدم الحالي
```

##### Order Management
```
GET    /api/orders                 - قائمة الطلبات للمستخدم الحالي
POST   /api/orders                 - إنشاء طلب جديد
GET    /api/orders/{id}            - عرض طلب محدد
PUT    /api/orders/{id}            - تحديث حالة الطلب
DELETE /api/orders/{id}            - حذف طلب (المرسل فقط)
GET    /api/orders/sent/list       - الطلبات المُرسلة من المستخدم
GET    /api/orders/received/list   - الطلبات المُستلمة للمستخدم
```

##### Report Management
```
GET    /api/reports                    - قائمة التقارير (Admin only)
POST   /api/reports                    - إرسال تقرير جديد
GET    /api/reports/{id}               - عرض تقرير محدد (Admin only)
DELETE /api/reports/{id}               - حذف تقرير (Admin only)
GET    /api/my-reports                 - التقارير المُرسلة من المستخدم الحالي

# Report Reasons Management (Admin only)
POST   /api/report-reasons             - إضافة سبب تقرير جديد
PUT    /api/report-reasons/{id}        - تحديث سبب تقرير
DELETE /api/report-reasons/{id}        - حذف سبب تقرير
```

### 🔑 Authentication Headers

لجميع الـ Protected Routes، يجب إرسال header التالي:

```
Authorization: Bearer {your_api_token}
```

### 📝 Response Format

جميع الـ API responses تتبع التنسيق التالي:

#### Success Response
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // Response data here
    }
}
```

#### Error Response
```json
{
    "success": false,
    "message": "Error message",
    "errors": {
        // Validation errors (if any)
    }
}
```

### 🚀 Getting Started

1. **التسجيل**: `POST /api/register`
2. **تسجيل الدخول**: `POST /api/login` - ستحصل على token
3. **استخدام Token**: أضف `Authorization: Bearer {token}` في headers
4. **الوصول للـ Protected Routes**: يمكنك الآن الوصول لجميع الـ endpoints المحمية

### 🛡️ Security Features

- **Token-based Authentication**: باستخدام Laravel Sanctum
- **Admin Middleware**: للتحكم في الصلاحيات
- **Input Validation**: جميع المدخلات مُتحقق منها
- **CORS Support**: مُفعل للـ frontend applications
- **Rate Limiting**: حماية من الـ abuse

### 📊 Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Server Error
