<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Blood;
use App\Services\SMSService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;
use Mockery;

class AccountVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected $smsService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock SMS Service
        $this->smsService = Mockery::mock(SMSService::class);
        $this->app->instance(SMSService::class, $this->smsService);
    }

    /** @test */
    public function user_registration_generates_verification_code_and_sends_sms()
    {
        // Create blood type
        $blood = Blood::factory()->create();

        // Mock SMS service
        $this->smsService
            ->shouldReceive('send')
            ->once()
            ->with('+************', Mockery::type('string'))
            ->andReturn(true);

        $userData = [
            'name' => 'Test User',
            'phone' => '+************',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'gender' => true,
            'birthdate' => '1990-01-01',
            'blood_id' => $blood->id,
        ];

        $response = $this->postJson('/api/register', $userData);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'User registered successfully. Verification code sent to your phone.',
                'data' => [
                    'verification_required' => true
                ]
            ]);

        // Check user was created with verification code
        $user = User::where('phone', '+************')->first();
        $this->assertNotNull($user);
        $this->assertNotNull($user->verification_code);
        $this->assertNotNull($user->verification_code_expired_at);
        $this->assertFalse($user->status); // Should not be verified yet
        $this->assertEquals(6, strlen($user->verification_code)); // Code should be 6 digits
    }

    /** @test */
    public function user_can_verify_account_with_valid_code()
    {
        // Create user with verification code
        $user = User::factory()->create([
            'phone' => '+************',
            'status' => false,
            'verification_code' => '123456',
            'verification_code_expired_at' => Carbon::now()->addMinutes(5),
        ]);

        $response = $this->postJson('/api/verify-account', [
            'phone' => '+************',
            'verification_code' => '123456'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Account verified successfully'
            ]);

        // Check user is now verified
        $user->refresh();
        $this->assertTrue($user->status);
        $this->assertNull($user->verification_code);
        $this->assertNull($user->verification_code_expired_at);
    }

    /** @test */
    public function verification_fails_with_invalid_code()
    {
        $user = User::factory()->create([
            'phone' => '+************',
            'status' => false,
            'verification_code' => '123456',
            'verification_code_expired_at' => Carbon::now()->addMinutes(5),
        ]);

        $response = $this->postJson('/api/verify-account', [
            'phone' => '+************',
            'verification_code' => '654321' // Wrong code
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Invalid verification code'
            ]);

        // User should still be unverified
        $user->refresh();
        $this->assertFalse($user->status);
    }

    /** @test */
    public function verification_fails_with_expired_code()
    {
        $user = User::factory()->create([
            'phone' => '+************',
            'status' => false,
            'verification_code' => '123456',
            'verification_code_expired_at' => Carbon::now()->subMinutes(5), // Expired
        ]);

        $response = $this->postJson('/api/verify-account', [
            'phone' => '+************',
            'verification_code' => '123456'
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Verification code has expired'
            ]);

        // User should still be unverified
        $user->refresh();
        $this->assertFalse($user->status);
    }

    /** @test */
    public function cannot_verify_already_verified_account()
    {
        $user = User::factory()->create([
            'phone' => '+************',
            'status' => true, // Already verified
            'verification_code' => '123456',
            'verification_code_expired_at' => Carbon::now()->addMinutes(5),
        ]);

        $response = $this->postJson('/api/verify-account', [
            'phone' => '+************',
            'verification_code' => '123456'
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Account is already verified'
            ]);
    }

    /** @test */
    public function can_resend_verification_code()
    {
        $user = User::factory()->create([
            'phone' => '+************',
            'status' => false,
            'verification_code' => '123456',
            'verification_code_expired_at' => Carbon::now()->subMinutes(5), // Expired
        ]);

        // Mock SMS service
        $this->smsService
            ->shouldReceive('send')
            ->once()
            ->with('+************', Mockery::type('string'))
            ->andReturn(true);

        $response = $this->postJson('/api/resend-verification-code', [
            'phone' => '+************'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'New verification code sent to your phone'
            ]);

        // Check user has new verification code
        $user->refresh();
        $this->assertNotEquals('123456', $user->verification_code); // Should be different
        $this->assertTrue(Carbon::now()->isBefore($user->verification_code_expired_at)); // Should be in future
    }

    /** @test */
    public function cannot_resend_code_for_verified_account()
    {
        $user = User::factory()->create([
            'phone' => '+************',
            'status' => true, // Already verified
        ]);

        $response = $this->postJson('/api/resend-verification-code', [
            'phone' => '+************'
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Account is already verified'
            ]);
    }

    /** @test */
    public function validation_fails_for_invalid_phone_number()
    {
        $response = $this->postJson('/api/verify-account', [
            'phone' => 'invalid-phone',
            'verification_code' => '123456'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone']);
    }

    /** @test */
    public function validation_fails_for_invalid_verification_code_length()
    {
        $response = $this->postJson('/api/verify-account', [
            'phone' => '+************',
            'verification_code' => '12345' // Too short
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['verification_code']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
