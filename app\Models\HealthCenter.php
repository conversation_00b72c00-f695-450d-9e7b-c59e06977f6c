<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HealthCenter extends Model
{
    use HasFactory;

    protected $table = 'health_center';

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'image',
        'city_id',
        'status',
        'more_info',
        'user_id',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the city that owns this health center
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the state through city
     */
    public function state()
    {
        return $this->hasOneThrough(State::class, City::class, 'id', 'id', 'city_id', 'state_id');
    }

    /**
     * Get the user who manages this health center
     */
    public function manager()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get all users associated with this health center (many-to-many relationship)
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'health_center_users')
                    ->withTimestamps();
    }

    /**
     * Get all health_center_users pivot records
     */
    public function healthCenterUsers()
    {
        return $this->hasMany(HealthCenterUser::class);
    }

    /**
     * Get all orders for this health center
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all orders quantity for this health center
     */
    public function ordersQuantity()
    {
        return $this->hasMany(OrdersQuantity::class);
    }
}
