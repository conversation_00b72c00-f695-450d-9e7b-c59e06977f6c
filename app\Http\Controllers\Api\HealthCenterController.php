<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\HealthCenter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class HealthCenterController extends Controller
{
    /**
     * Display a listing of health centers
     */
    public function index(Request $request)
    {
        $query = HealthCenter::with(['city.state', 'manager'])
            ->where('status', true);

        // Filter by city if provided
        if ($request->has('city_id')) {
            $query->where('city_id', $request->city_id);
        }

        // Filter by state through city relationship
        if ($request->has('state_id')) {
            $query->whereHas('city', function($q) use ($request) {
                $q->where('state_id', $request->state_id);
            });
        }

        $healthCenters = $query->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $healthCenters
        ]);
    }

    /**
     * Store a newly created health center (Admin only)
     */
    public function store(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'city_id' => 'required|exists:cities,id',
            'user_id' => 'required|exists:users,id',
            'image' => 'sometimes|string',
            'more_info' => 'sometimes|string',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $healthCenter = HealthCenter::create([
            'name' => $request->name,
            'address' => $request->address,
            'phone' => $request->phone,
            'email' => $request->email,
            'city_id' => $request->city_id,
            'user_id' => $request->user_id,
            'image' => $request->image,
            'more_info' => $request->more_info,
            'status' => $request->status ?? true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Health center created successfully',
            'data' => $healthCenter->load(['city.state', 'manager'])
        ], 201);
    }

    /**
     * Display the specified health center
     */
    public function show(HealthCenter $healthCenter)
    {
        return response()->json([
            'success' => true,
            'data' => $healthCenter->load(['city.state', 'manager', 'users'])
        ]);
    }

    /**
     * Update the specified health center
     */
    public function update(Request $request, HealthCenter $healthCenter)
    {
        $user = $request->user();

        // Check if user is admin or the manager of this health center
        if (!$user->is_admin && $healthCenter->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin or health center manager access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'address' => 'sometimes|string|max:255',
            'phone' => 'sometimes|string|max:20',
            'email' => 'sometimes|email|max:255',
            'city_id' => 'sometimes|exists:cities,id',
            'user_id' => 'sometimes|exists:users,id',
            'image' => 'sometimes|string',
            'more_info' => 'sometimes|string',
            'status' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Only admin can change user_id and status
        $updateData = $request->only(['name', 'address', 'phone', 'email', 'city_id', 'image', 'more_info']);
        
        if ($user->is_admin) {
            $updateData = array_merge($updateData, $request->only(['user_id', 'status']));
        }

        $healthCenter->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Health center updated successfully',
            'data' => $healthCenter->fresh()->load(['city.state', 'manager'])
        ]);
    }

    /**
     * Remove the specified health center (Admin only)
     */
    public function destroy(Request $request, HealthCenter $healthCenter)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Check if health center has orders
        if ($healthCenter->orders()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete health center. It has associated orders.'
            ], 400);
        }

        $healthCenter->delete();

        return response()->json([
            'success' => true,
            'message' => 'Health center deleted successfully'
        ]);
    }

    /**
     * Get health centers managed by the authenticated user
     */
    public function managed(Request $request)
    {
        $healthCenters = HealthCenter::where('user_id', $request->user()->id)
            ->with(['city.state', 'users'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $healthCenters
        ]);
    }

    /**
     * Get health centers associated with the authenticated user
     */
    public function associated(Request $request)
    {
        $user = $request->user();
        $healthCenters = $user->healthCenters()->with(['city.state', 'manager'])->get();

        return response()->json([
            'success' => true,
            'data' => $healthCenters
        ]);
    }
}
