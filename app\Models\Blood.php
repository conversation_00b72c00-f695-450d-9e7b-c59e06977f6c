<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Blood extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'blood';

    protected $fillable = [
        'name',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get all users with this blood type
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all orders for this blood type
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all orders quantity for this blood type
     */
    public function ordersQuantity()
    {
        return $this->hasMany(OrdersQuantity::class);
    }
}
