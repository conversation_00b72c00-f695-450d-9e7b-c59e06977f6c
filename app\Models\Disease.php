<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Disease extends Model
{
    use HasFactory;

    protected $table = 'disease';

    protected $fillable = [
        'name',
        'status',
        'more_info',
        'user_id',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the user who created this disease
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get all users who have this disease (many-to-many relationship)
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'disease_users')
                    ->withTimestamps();
    }

    /**
     * Get all disease_users pivot records
     */
    public function diseaseUsers()
    {
        return $this->hasMany(DiseaseUser::class);
    }
}
