<?php

namespace App\Jobs;

use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class SendBulkNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userIds;
    protected $title;
    protected $message;
    protected $data;
    protected $channels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 300; // 5 minutes for bulk operations

    /**
     * Create a new job instance.
     *
     * @param array $userIds
     * @param string $title
     * @param string $message
     * @param array $data
     * @param array $channels
     */
    public function __construct(array $userIds, string $title, string $message, array $data = [], array $channels = ['push'])
    {
        $this->userIds = $userIds;
        $this->title = $title;
        $this->message = $message;
        $this->data = $data;
        $this->channels = $channels;
    }

    /**
     * Execute the job.
     *
     * @param NotificationService $notificationService
     * @return void
     */
    public function handle(NotificationService $notificationService): void
    {
        try {
            $users = \App\Models\User::whereIn('id', $this->userIds)->get();
            
            if ($users->isEmpty()) {
                Log::warning('No users found for bulk notification job', ['user_ids' => $this->userIds]);
                return;
            }

            $result = $notificationService->sendBulkNotification(
                $users->toArray(),
                $this->title,
                $this->message,
                $this->data,
                $this->channels
            );

            Log::info('Bulk notification job completed successfully', [
                'user_count' => count($this->userIds),
                'title' => $this->title,
                'channels' => $this->channels,
                'success_count' => count(array_filter($result, function($r) { 
                    return isset($r['push']) && $r['push'] === true; 
                }))
            ]);

        } catch (Exception $e) {
            Log::error('Bulk notification job failed: ' . $e->getMessage(), [
                'user_count' => count($this->userIds),
                'title' => $this->title,
                'error' => $e->getMessage()
            ]);

            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param Exception $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('Bulk notification job failed permanently', [
            'user_count' => count($this->userIds),
            'title' => $this->title,
            'channels' => $this->channels,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);
    }
}
