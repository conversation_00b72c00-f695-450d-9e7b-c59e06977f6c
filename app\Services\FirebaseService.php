<?php

namespace App\Services;

use <PERSON>reait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Exception\MessagingException;
use Illuminate\Support\Facades\Log;
use Exception;

class FirebaseService
{
    protected $messaging;
    protected $factory;

    public function __construct()
    {
        try {
            $this->factory = (new Factory)
                ->withServiceAccount(config('firebase.credentials.file'))
                ->withProjectId(config('firebase.project_id'));
            
            $this->messaging = $this->factory->createMessaging();
        } catch (Exception $e) {
            Log::error('Firebase initialization failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send notification to a single device
     *
     * @param string $fcmToken
     * @param string $title
     * @param string $body
     * @param array $data
     * @param array $options
     * @return bool
     */
    public function sendToDevice(string $fcmToken, string $title, string $body, array $data = [], array $options = []): bool
    {
        try {
            $notification = Notification::create($title, $body);
            
            $message = CloudMessage::withTarget('token', $fcmToken)
                ->withNotification($notification)
                ->withData($data);

            // Add Android specific configuration
            if (isset($options['android'])) {
                $androidConfig = AndroidConfig::fromArray($options['android']);
                $message = $message->withAndroidConfig($androidConfig);
            }

            // Add iOS specific configuration
            if (isset($options['apns'])) {
                $apnsConfig = ApnsConfig::fromArray($options['apns']);
                $message = $message->withApnsConfig($apnsConfig);
            }

            $this->messaging->send($message);
            
            Log::info('Firebase notification sent successfully', [
                'token' => $fcmToken,
                'title' => $title,
                'body' => $body
            ]);

            return true;
        } catch (MessagingException $e) {
            Log::error('Firebase messaging error: ' . $e->getMessage(), [
                'token' => $fcmToken,
                'title' => $title,
                'body' => $body,
                'error' => $e->getMessage()
            ]);
            return false;
        } catch (Exception $e) {
            Log::error('Firebase service error: ' . $e->getMessage(), [
                'token' => $fcmToken,
                'title' => $title,
                'body' => $body,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send notification to multiple devices
     *
     * @param array $fcmTokens
     * @param string $title
     * @param string $body
     * @param array $data
     * @param array $options
     * @return array
     */
    public function sendToMultipleDevices(array $fcmTokens, string $title, string $body, array $data = [], array $options = []): array
    {
        $results = [];
        
        foreach ($fcmTokens as $token) {
            $results[$token] = $this->sendToDevice($token, $title, $body, $data, $options);
        }

        return $results;
    }

    /**
     * Send notification to a topic
     *
     * @param string $topic
     * @param string $title
     * @param string $body
     * @param array $data
     * @param array $options
     * @return bool
     */
    public function sendToTopic(string $topic, string $title, string $body, array $data = [], array $options = []): bool
    {
        try {
            $notification = Notification::create($title, $body);
            
            $message = CloudMessage::withTarget('topic', $topic)
                ->withNotification($notification)
                ->withData($data);

            // Add Android specific configuration
            if (isset($options['android'])) {
                $androidConfig = AndroidConfig::fromArray($options['android']);
                $message = $message->withAndroidConfig($androidConfig);
            }

            // Add iOS specific configuration
            if (isset($options['apns'])) {
                $apnsConfig = ApnsConfig::fromArray($options['apns']);
                $message = $message->withApnsConfig($apnsConfig);
            }

            $this->messaging->send($message);
            
            Log::info('Firebase topic notification sent successfully', [
                'topic' => $topic,
                'title' => $title,
                'body' => $body
            ]);

            return true;
        } catch (MessagingException $e) {
            Log::error('Firebase topic messaging error: ' . $e->getMessage(), [
                'topic' => $topic,
                'title' => $title,
                'body' => $body,
                'error' => $e->getMessage()
            ]);
            return false;
        } catch (Exception $e) {
            Log::error('Firebase topic service error: ' . $e->getMessage(), [
                'topic' => $topic,
                'title' => $title,
                'body' => $body,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Subscribe device to topic
     *
     * @param string|array $fcmTokens
     * @param string $topic
     * @return bool
     */
    public function subscribeToTopic($fcmTokens, string $topic): bool
    {
        try {
            $tokens = is_array($fcmTokens) ? $fcmTokens : [$fcmTokens];
            $this->messaging->subscribeToTopic($topic, $tokens);
            
            Log::info('Devices subscribed to topic successfully', [
                'topic' => $topic,
                'tokens_count' => count($tokens)
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Firebase topic subscription error: ' . $e->getMessage(), [
                'topic' => $topic,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Unsubscribe device from topic
     *
     * @param string|array $fcmTokens
     * @param string $topic
     * @return bool
     */
    public function unsubscribeFromTopic($fcmTokens, string $topic): bool
    {
        try {
            $tokens = is_array($fcmTokens) ? $fcmTokens : [$fcmTokens];
            $this->messaging->unsubscribeFromTopic($topic, $tokens);
            
            Log::info('Devices unsubscribed from topic successfully', [
                'topic' => $topic,
                'tokens_count' => count($tokens)
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Firebase topic unsubscription error: ' . $e->getMessage(), [
                'topic' => $topic,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Validate FCM token
     *
     * @param string $fcmToken
     * @return bool
     */
    public function validateToken(string $fcmToken): bool
    {
        try {
            $message = CloudMessage::withTarget('token', $fcmToken)
                ->withData(['test' => 'validation']);

            $this->messaging->validate($message);
            return true;
        } catch (Exception $e) {
            Log::warning('Invalid FCM token', [
                'token' => $fcmToken,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
