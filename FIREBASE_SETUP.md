# إعداد Firebase للإشعارات والرسائل في مشروع بنك الدم

## نظرة عامة

تم تطوير نظام شامل للإشعارات والرسائل النصية لمشروع بنك الدم باستخدام Firebase Cloud Messaging (FCM) وخدمات SMS متعددة.

## المميزات

- ✅ إرسال إشعارات push عبر Firebase Cloud Messaging
- ✅ إرسال رسائل SMS عبر Twilio أو Nexmo
- ✅ نظام قوالب للرسائل باللغة العربية
- ✅ إشعارات تلقائية للطلبات (إنشاء، قبول، رفض، إكمال)
- ✅ إشعارات الطوارئ لطلبات الدم العاجلة
- ✅ نظام Jobs للإرسال غير المتزامن
- ✅ API endpoints لإدارة الإشعارات
- ✅ اختبارات شاملة للنظام

## الخدمات المطورة

### 1. FirebaseService
خدمة للتعامل مع Firebase Cloud Messaging:
- إرسال إشعارات لجهاز واحد أو متعدد
- إرسال إشعارات للمواضيع (Topics)
- الاشتراك وإلغاء الاشتراك في المواضيع
- التحقق من صحة FCM tokens

### 2. SMSService
خدمة لإرسال الرسائل النصية:
- دعم Twilio و Nexmo
- تنسيق أرقام الهواتف الدولية
- إرسال رسائل جماعية
- دعم النصوص العربية (Unicode)

### 3. NotificationService
خدمة موحدة لإدارة الإشعارات:
- إرسال عبر قنوات متعددة (Push + SMS)
- إشعارات الطلبات التلقائية
- إشعارات الطوارئ
- إدارة الاشتراكات في المواضيع

### 4. MessageTemplateService
خدمة إدارة قوالب الرسائل:
- قوالب باللغة العربية
- استبدال المتغيرات التلقائي
- دعم قنوات متعددة (Push, SMS, Email)
- التحقق من صحة البيانات

## إعداد Firebase

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم مشروع موجود
3. فعّل Firebase Cloud Messaging

### 2. الحصول على ملف الاعتمادات
1. في إعدادات المشروع، اذهب إلى "Service accounts"
2. انقر على "Generate new private key"
3. احفظ الملف في مجلد `storage/app/firebase/`

### 3. إعداد متغيرات البيئة
أضف المتغيرات التالية إلى ملف `.env`:

```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CREDENTIALS_PATH=storage/app/firebase/your-credentials.json
FIREBASE_DATABASE_URL=https://your-project.firebaseio.com
FIREBASE_STORAGE_BUCKET=your-project.appspot.com

# SMS Configuration
SMS_PROVIDER=twilio
TWILIO_SID=your-twilio-sid
TWILIO_TOKEN=your-twilio-token
TWILIO_FROM=your-twilio-phone-number

# Alternative SMS providers
NEXMO_KEY=your-nexmo-key
NEXMO_SECRET=your-nexmo-secret
NEXMO_FROM=your-nexmo********id
```

## إعداد SMS

### Firebase SMS (الخيار الجديد)
1. في Firebase Console، فعّل Authentication
2. فعّل Phone Authentication provider
3. احصل على Web API Key من إعدادات المشروع
4. (اختياري) أنشئ Cloud Function لإرسال رسائل مخصصة

### Twilio
1. أنشئ حساب في [Twilio](https://www.twilio.com/)
2. احصل على Account SID و Auth Token
3. اشترِ رقم هاتف للإرسال

### Nexmo/Vonage
1. أنشئ حساب في [Vonage](https://www.vonage.com/)
2. احصل على API Key و Secret
3. أعد Sender ID

## API Endpoints

### إدارة FCM Tokens
```http
POST /api/notifications/fcm-token
DELETE /api/notifications/fcm-token
```

### الإشعارات
```http
POST /api/notifications/test
POST /api/notifications/emergency-blood-request
POST /api/notifications/custom (Admin only)
GET /api/notifications/preferences
```

### الاشتراكات
```http
POST /api/notifications/subscribe-blood-type
POST /api/notifications/unsubscribe-blood-type
```

### Firebase SMS (جديد)
```http
POST /api/firebase-sms/send-verification
POST /api/firebase-sms/verify-phone
GET /api/firebase-sms/status
POST /api/firebase-sms/format-phone
POST /api/firebase-sms/send-custom (Admin only)
POST /api/firebase-sms/message-info
```

## استخدام النظام

### إرسال إشعار بسيط
```php
use App\Services\NotificationService;

$notificationService = app(NotificationService::class);

$notificationService->sendToUser(
    $user,
    'عنوان الإشعار',
    'محتوى الإشعار',
    ['key' => 'value'], // بيانات إضافية
    ['push', 'sms'] // القنوات
);
```

### إرسال إشعار طلب
```php
use App\Jobs\SendOrderNotificationJob;

SendOrderNotificationJob::dispatch($orderId, 'new_order', ['push', 'sms']);
```

### إرسال إشعار طوارئ
```php
$notificationService->sendEmergencyBloodRequest(
    'A+', // فصيلة الدم
    'مستشفى الملك فهد', // الموقع
    '+966501234567' // معلومات التواصل
);
```

## Jobs المتاحة

- `SendNotificationJob`: إرسال إشعار لمستخدم واحد
- `SendOrderNotificationJob`: إرسال إشعار طلب
- `SendBulkNotificationJob`: إرسال إشعارات جماعية

## تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
php artisan test

# تشغيل اختبارات الإشعارات فقط
php artisan test tests/Feature/NotificationTest.php

# تشغيل اختبارات القوالب
php artisan test tests/Unit/MessageTemplateServiceTest.php
```

## تشغيل Queue

لتشغيل Jobs الإشعارات:

```bash
# تشغيل queue worker
php artisan queue:work

# أو في الخلفية
php artisan queue:work --daemon
```

## استكشاف الأخطاء

### مشاكل Firebase
- تأكد من صحة ملف الاعتمادات
- تحقق من Project ID
- راجع logs في `storage/logs/laravel.log`

### مشاكل SMS
- تأكد من صحة بيانات الاعتماد
- تحقق من تنسيق أرقام الهواتف
- راجع حدود الإرسال لمزود الخدمة

## الأمان

- احم ملف اعتمادات Firebase
- لا تشارك API keys في الكود
- استخدم HTTPS للاتصالات
- راجع permissions للمستخدمين

## الدعم

للمساعدة أو الاستفسارات، راجع:
- [Firebase Documentation](https://firebase.google.com/docs)
- [Twilio Documentation](https://www.twilio.com/docs)
- [Laravel Queue Documentation](https://laravel.com/docs/queues)
