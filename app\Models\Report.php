<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Report extends Model
{
    use HasFactory;

    protected $fillable = [
        'more_info',
        'from_user_id',
        'to_user_id',
        'report_reason_id',
    ];

    /**
     * Get the user who made the report
     */
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * Get the user who is being reported
     */
    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    /**
     * Get the reason for this report
     */
    public function reportReason()
    {
        return $this->belongsTo(ReportsReason::class, 'report_reason_id');
    }
}
