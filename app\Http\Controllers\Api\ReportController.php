<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Report;
use App\Models\ReportsReason;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReportController extends Controller
{
    /**
     * Display a listing of reports (Admin only)
     */
    public function index(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $reports = Report::with(['fromUser', 'toUser', 'reportReason'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $reports
        ]);
    }

    /**
     * Store a newly created report
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'to_user_id' => 'required|exists:users,id',
            'report_reason_id' => 'required|exists:reports_reasons,id',
            'more_info' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if user is trying to report themselves
        if ($request->to_user_id == $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot report yourself'
            ], 400);
        }

        // Check if user already reported this user for the same reason
        $existingReport = Report::where('from_user_id', $request->user()->id)
            ->where('to_user_id', $request->to_user_id)
            ->where('report_reason_id', $request->report_reason_id)
            ->first();

        if ($existingReport) {
            return response()->json([
                'success' => false,
                'message' => 'You have already reported this user for this reason'
            ], 400);
        }

        $report = Report::create([
            'from_user_id' => $request->user()->id,
            'to_user_id' => $request->to_user_id,
            'report_reason_id' => $request->report_reason_id,
            'more_info' => $request->more_info,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Report submitted successfully',
            'data' => $report->load(['fromUser', 'toUser', 'reportReason'])
        ], 201);
    }

    /**
     * Display the specified report (Admin only)
     */
    public function show(Request $request, Report $report)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $report->load(['fromUser', 'toUser', 'reportReason'])
        ]);
    }

    /**
     * Remove the specified report (Admin only)
     */
    public function destroy(Request $request, Report $report)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $report->delete();

        return response()->json([
            'success' => true,
            'message' => 'Report deleted successfully'
        ]);
    }

    /**
     * Get reports made by the authenticated user
     */
    public function myReports(Request $request)
    {
        $reports = Report::where('from_user_id', $request->user()->id)
            ->with(['toUser', 'reportReason'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $reports
        ]);
    }

    /**
     * Get report reasons
     */
    public function reasons()
    {
        $reasons = ReportsReason::select('id','reason')->where('status', true)->get();
        return response()->json([
            'success' => true,
            'data' => $reasons
        ]);
    }

    /**
     * Store a new report reason (Admin only)
     */
    public function storeReason(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:255|unique:reports_reasons',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $reason = ReportsReason::create([
            'reason' => $request->reason,
            'status' => $request->status ?? true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Report reason created successfully',
            'data' => $reason
        ], 201);
    }

    /**
     * Update report reason (Admin only)
     */
    public function updateReason(Request $request, ReportsReason $reason)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'sometimes|string|max:255|unique:reports_reasons,reason,' . $reason->id,
            'status' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $reason->update($request->only(['reason', 'status']));

        return response()->json([
            'success' => true,
            'message' => 'Report reason updated successfully',
            'data' => $reason
        ]);
    }

    /**
     * Delete report reason (Admin only)
     */
    public function destroyReason(Request $request, ReportsReason $reason)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Check if reason is being used
        if ($reason->reports()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete reason. It is being used in reports.'
            ], 400);
        }

        $reason->delete();

        return response()->json([
            'success' => true,
            'message' => 'Report reason deleted successfully'
        ]);
    }
}
