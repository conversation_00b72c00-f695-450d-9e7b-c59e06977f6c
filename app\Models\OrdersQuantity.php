<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrdersQuantity extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'orders_quantity';

    protected $fillable = [
        'quantity',
        'status',
        'date',
        'more_info',
        'user_id',
        'blood_id',
        'health_center_id',
    ];

    protected $casts = [
        'status' => 'boolean',
        'date' => 'date',
    ];

    /**
     * Get the user who created this order quantity
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the blood type for this order quantity
     */
    public function blood()
    {
        return $this->belongsTo(Blood::class);
    }

    /**
     * Get the health center for this order quantity
     */
    public function healthCenter()
    {
        return $this->belongsTo(HealthCenter::class);
    }

    /**
     * Get all donors associated with this order quantity (many-to-many relationship)
     */
    public function donors()
    {
        return $this->belongsToMany(User::class, 'orders_quantity_users', 'orders_quantity_id', 'donor_id')
                    ->withPivot('quantity', 'status')
                    ->withTimestamps();
    }

    /**
     * Get all orders_quantity_users pivot records
     */
    public function ordersQuantityUsers()
    {
        return $this->hasMany(OrdersQuantityUser::class);
    }
}
