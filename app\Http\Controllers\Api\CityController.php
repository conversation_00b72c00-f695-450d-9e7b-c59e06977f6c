<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\City;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CityController extends Controller
{
    /**
     * Display a listing of cities
     */
    public function index(Request $request)
    {
        $query = City::with(['state' => function ($q) {
            $q->select('id', 'name'); // فقط id و name للـ State
        }])
    ->where('status', true)
    ->select('id', 'name', 'state_id'); // فقط id, name, state_id للـ City

        // فلترة بالـ state لو موجود
        if ($request->has('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        $cities = $query->get();


        return response()->json([
            'success' => true,
            'data' => $cities
        ]);
    }

    /**
     * Store a newly created city (Admin only)
     */
    public function store(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:cities',
            'state_id' => 'required|exists:states,id',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $city = City::create([
            'name' => $request->name,
            'state_id' => $request->state_id,
            'status' => $request->status ?? true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'City created successfully',
            'data' => $city->load('state')
        ], 201);
    }

    /**
     * Display the specified city
     */
    public function show(City $city)
    {
        return response()->json([
            'success' => true,
            'data' => $city->load(['state', 'healthCenters' => function ($query) {
                $query->where('status', true);
            }])
        ]);
    }

    /**
     * Update the specified city (Admin only)
     */
    public function update(Request $request, City $city)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:cities,name,' . $city->id,
            'state_id' => 'sometimes|exists:states,id',
            'status' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $city->update($request->only(['name', 'state_id', 'status']));

        return response()->json([
            'success' => true,
            'message' => 'City updated successfully',
            'data' => $city->fresh()->load('state')
        ]);
    }

    /**
     * Remove the specified city (Admin only)
     */
    public function destroy(Request $request, City $city)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Check if city has health centers
        if ($city->healthCenters()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete city. It has associated health centers.'
            ], 400);
        }

        $city->delete();

        return response()->json([
            'success' => true,
            'message' => 'City deleted successfully'
        ]);
    }

    /**
     * Get health centers in the specified city
     */
    public function healthCenters(City $city)
    {
        $healthCenters = $city->healthCenters()
            ->where('status', true)
            ->with('manager')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'city' => $city->load('state'),
                'health_centers' => $healthCenters
            ]
        ]);
    }
}
