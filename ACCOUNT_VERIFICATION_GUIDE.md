# دليل التحقق من الحساب عبر SMS - مشروع بنك الدم

## نظرة عامة

تم إضافة نظام التحقق من الحساب عبر رسائل SMS إلى مشروع بنك الدم. يتطلب هذا النظام من المستخدمين الجدد التحقق من أرقام هواتفهم قبل تفعيل حساباتهم.

## الميزات الجديدة

### 1. التسجيل مع كود التحقق
- عند التسجيل، يتم إنشاء كود تحقق مكون من 6 أرقام
- يتم إرسال الكود عبر SMS إلى رقم هاتف المستخدم
- الكود صالح لمدة 10 دقائق
- الحساب يبقى غير مفعل حتى التحقق من الكود

### 2. التحقق من الحساب
- المستخدم يدخل رقم الهاتف وكود التحقق
- يتم التحقق من صحة الكود وانتهاء صلاحيته
- عند النجاح، يتم تفعيل الحساب وحذف كود التحقق

### 3. إعادة إرسال كود التحقق
- في حالة انتهاء صلاحية الكود أو عدم وصوله
- يتم إنشاء كود جديد وإرساله عبر SMS
- الكود الجديد صالح لمدة 10 دقائق أخرى

## API Endpoints

### 1. التسجيل (محدث)
```http
POST /api/register
Content-Type: application/json

{
    "name": "أحمد محمد",
    "phone": "+************",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "gender": true,
    "birthdate": "1990-01-01",
    "blood_id": 1
}
```

**الاستجابة:**
```json
{
    "success": true,
    "message": "User registered successfully. Verification code sent to your phone.",
    "data": {
        "user": {
            "id": 1,
            "name": "أحمد محمد",
            "phone": "+************",
            "email": "<EMAIL>",
            "status": false,
            "blood": {
                "id": 1,
                "name": "A+"
            }
        },
        "token": "1|abc123...",
        "token_type": "Bearer",
        "verification_required": true
    }
}
```

### 2. التحقق من الحساب (جديد)
```http
POST /api/verify-account
Content-Type: application/json

{
    "phone": "+************",
    "verification_code": "123456"
}
```

**الاستجابة الناجحة:**
```json
{
    "success": true,
    "message": "Account verified successfully",
    "data": {
        "user": {
            "id": 1,
            "name": "أحمد محمد",
            "phone": "+************",
            "status": true,
            "blood": {
                "id": 1,
                "name": "A+"
            }
        }
    }
}
```

**الاستجابة في حالة الخطأ:**
```json
{
    "success": false,
    "message": "Invalid verification code"
}
```

### 3. إعادة إرسال كود التحقق (جديد)
```http
POST /api/resend-verification-code
Content-Type: application/json

{
    "phone": "+************"
}
```

**الاستجابة:**
```json
{
    "success": true,
    "message": "New verification code sent to your phone"
}
```

## حالات الخطأ

### 1. كود التحقق غير صحيح
```json
{
    "success": false,
    "message": "Invalid verification code"
}
```

### 2. كود التحقق منتهي الصلاحية
```json
{
    "success": false,
    "message": "Verification code has expired"
}
```

### 3. الحساب مفعل مسبقاً
```json
{
    "success": false,
    "message": "Account is already verified"
}
```

### 4. المستخدم غير موجود
```json
{
    "success": false,
    "message": "User not found"
}
```

### 5. أخطاء التحقق من صحة البيانات
```json
{
    "success": false,
    "message": "Validation errors",
    "errors": {
        "phone": ["The phone field is required."],
        "verification_code": ["The verification code must be 6 characters."]
    }
}
```

## التغييرات في قاعدة البيانات

### جدول المستخدمين (users)
تم استخدام الحقول الموجودة مسبقاً:
- `verification_code`: كود التحقق (6 أرقام)
- `verification_code_expired_at`: تاريخ انتهاء صلاحية الكود
- `status`: حالة تفعيل الحساب (false = غير مفعل، true = مفعل)

## رسائل SMS

### رسالة التسجيل
```
كود التحقق الخاص بك في تطبيق بنك الدم هو: 123456. صالح لمدة 10 دقائق.
```

### رسالة إعادة الإرسال
```
كود التحقق الجديد الخاص بك في تطبيق بنك الدم هو: 654321. صالح لمدة 10 دقائق.
```

## الأمان

### 1. كود التحقق
- مكون من 6 أرقام عشوائية
- صالح لمدة 10 دقائق فقط
- يتم حذفه بعد التحقق الناجح
- لا يظهر في JSON responses (مخفي في User model)

### 2. التحقق من صحة البيانات
- التحقق من وجود رقم الهاتف في قاعدة البيانات
- التحقق من طول كود التحقق (6 أرقام بالضبط)
- التحقق من انتهاء صلاحية الكود

### 3. منع الإساءة
- لا يمكن إعادة إرسال الكود للحسابات المفعلة
- التحقق من صحة البيانات قبل المعالجة
- رسائل خطأ واضحة ومفيدة

## الاختبارات

تم إنشاء مجموعة شاملة من الاختبارات في `tests/Feature/AccountVerificationTest.php`:

1. **اختبار التسجيل**: التأكد من إنشاء كود التحقق وإرسال SMS
2. **اختبار التحقق الناجح**: التحقق من الكود الصحيح
3. **اختبار الكود الخاطئ**: رفض الكود غير الصحيح
4. **اختبار الكود المنتهي**: رفض الكود منتهي الصلاحية
5. **اختبار الحساب المفعل**: منع التحقق من الحسابات المفعلة مسبقاً
6. **اختبار إعادة الإرسال**: إنشاء كود جديد وإرساله
7. **اختبارات التحقق من صحة البيانات**: التأكد من صحة المدخلات

## تشغيل الاختبارات

```bash
# تشغيل جميع اختبارات التحقق من الحساب
php artisan test tests/Feature/AccountVerificationTest.php

# تشغيل اختبار محدد
php artisan test --filter test_user_can_verify_account_with_valid_code
```

## استخدام النظام في التطبيق

### 1. تدفق التسجيل
1. المستخدم يملأ نموذج التسجيل
2. يتم إرسال طلب POST إلى `/api/register`
3. يتم إنشاء الحساب مع `status = false`
4. يتم إنشاء كود التحقق وإرساله عبر SMS
5. يتم إرجاع `verification_required = true` في الاستجابة

### 2. تدفق التحقق
1. المستخدم يدخل كود التحقق المستلم
2. يتم إرسال طلب POST إلى `/api/verify-account`
3. يتم التحقق من الكود وانتهاء صلاحيته
4. عند النجاح، يتم تحديث `status = true`
5. يتم حذف كود التحقق

### 3. تدفق إعادة الإرسال
1. في حالة عدم وصول الكود أو انتهاء صلاحيته
2. يتم إرسال طلب POST إلى `/api/resend-verification-code`
3. يتم إنشاء كود جديد وإرساله عبر SMS

## الملاحظات المهمة

1. **الحسابات غير المفعلة**: يمكن للمستخدمين تسجيل الدخول حتى لو لم يفعلوا حساباتهم، لكن يُنصح بتقييد بعض الوظائف
2. **مدة صلاحية الكود**: 10 دقائق قابلة للتعديل في الكود
3. **تكامل SMS**: يعتمد على SMSService الموجود في المشروع
4. **الأمان**: جميع البيانات الحساسة محمية ومخفية

## التطوير المستقبلي

1. **Rate Limiting**: إضافة حدود على عدد محاولات إعادة الإرسال
2. **تسجيل العمليات**: تسجيل محاولات التحقق للمراقبة
3. **تخصيص الرسائل**: إمكانية تخصيص نصوص رسائل SMS
4. **دعم البريد الإلكتروني**: إضافة خيار التحقق عبر البريد الإلكتروني كبديل
