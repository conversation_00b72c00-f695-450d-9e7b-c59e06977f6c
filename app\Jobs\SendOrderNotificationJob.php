<?php

namespace App\Jobs;

use App\Models\Order;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class SendOrderNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderId;
    protected $notificationType;
    protected $channels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * Create a new job instance.
     *
     * @param int $orderId
     * @param string $notificationType
     * @param array $channels
     */
    public function __construct(int $orderId, string $notificationType, array $channels = ['push', 'sms'])
    {
        $this->orderId = $orderId;
        $this->notificationType = $notificationType;
        $this->channels = $channels;
    }

    /**
     * Execute the job.
     *
     * @param NotificationService $notificationService
     * @return void
     */
    public function handle(NotificationService $notificationService): void
    {
        try {
            $order = Order::with(['fromUser', 'toUser', 'blood', 'healthCenter'])->find($this->orderId);
            
            if (!$order) {
                Log::warning('Order not found for notification job', ['order_id' => $this->orderId]);
                return;
            }

            $result = $notificationService->sendOrderNotification(
                $order,
                $this->notificationType,
                $this->channels
            );

            Log::info('Order notification job completed successfully', [
                'order_id' => $this->orderId,
                'notification_type' => $this->notificationType,
                'channels' => $this->channels,
                'result' => $result
            ]);

        } catch (Exception $e) {
            Log::error('Order notification job failed: ' . $e->getMessage(), [
                'order_id' => $this->orderId,
                'notification_type' => $this->notificationType,
                'error' => $e->getMessage()
            ]);

            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param Exception $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('Order notification job failed permanently', [
            'order_id' => $this->orderId,
            'notification_type' => $this->notificationType,
            'channels' => $this->channels,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);
    }
}
