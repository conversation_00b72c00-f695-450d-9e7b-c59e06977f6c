{"info": {"name": "Blood Bank API", "description": "API Collection for Blood Bank System with Laravel Sanctum Authentication", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{api_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost/blod_bank/public/api", "type": "string"}, {"key": "api_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"**********\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"first_contact_phone\": \"**********\",\n    \"second_contact_phone\": \"**********\",\n    \"gender\": true,\n    \"birthdate\": \"1990-01-01\",\n    \"blood_id\": 1,\n    \"is_donor\": true\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}}}]}, {"name": "Blood Types", "item": [{"name": "Get Blood Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/blood-types", "host": ["{{base_url}}"], "path": ["blood-types"]}}}, {"name": "Get Blood Donors", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/blood/1/donors", "host": ["{{base_url}}"], "path": ["blood", "1", "donors"]}}}]}, {"name": "States & Cities", "item": [{"name": "Get States", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/states", "host": ["{{base_url}}"], "path": ["states"]}}}, {"name": "Get Cities", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/cities", "host": ["{{base_url}}"], "path": ["cities"]}}}]}, {"name": "Orders", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"message\": \"Urgent blood needed\",\n    \"to_user_id\": 2,\n    \"blood_id\": 1,\n    \"health_center_id\": 1,\n    \"date\": \"2024-01-01 10:00:00\"\n}"}, "url": {"raw": "{{base_url}}/orders", "host": ["{{base_url}}"], "path": ["orders"]}}}, {"name": "Get My Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders", "host": ["{{base_url}}"], "path": ["orders"]}}}]}, {"name": "Reports", "item": [{"name": "Get Report Reasons", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/report-reasons", "host": ["{{base_url}}"], "path": ["report-reasons"]}}}, {"name": "Submit Report", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"to_user_id\": 2,\n    \"report_reason_id\": 1,\n    \"more_info\": \"Additional information about the report\"\n}"}, "url": {"raw": "{{base_url}}/reports", "host": ["{{base_url}}"], "path": ["reports"]}}}]}]}