<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class State extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get all cities in this state
     */
    public function cities()
    {
        return $this->hasMany(City::class);
    }

    /**
     * Get all health centers in this state through cities
     */
    public function healthCenters()
    {
        return $this->hasManyThrough(HealthCenter::class, City::class);
    }
}
