<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReportsReason extends Model
{
    use HasFactory;

    protected $table = 'reports_reasons';

    protected $fillable = [
        'reason',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get all reports using this reason
     */
    public function reports()
    {
        return $this->hasMany(Report::class, 'report_reason_id');
    }
}
