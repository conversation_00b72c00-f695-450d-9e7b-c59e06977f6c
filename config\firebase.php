<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Firebase Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for Firebase services including
    | Firebase Cloud Messaging (FCM), Firestore, and other Firebase services.
    |
    */

    'project_id' => env('FIREBASE_PROJECT_ID'),

    'credentials' => [
        'file' => env('FIREBASE_CREDENTIALS_PATH'),
        'auto_discovery' => env('FIREBASE_AUTO_DISCOVERY', true),
    ],

    'database_url' => env('FIREBASE_DATABASE_URL'),

    'storage_bucket' => env('FIREBASE_STORAGE_BUCKET'),

    'messaging' => [
        'http_timeout' => env('FIREBASE_HTTP_TIMEOUT', 30),
        'guzzle_config' => [],
    ],

    'dynamic_links' => [
        'default_domain' => env('FIREBASE_DYNAMIC_LINKS_DEFAULT_DOMAIN'),
    ],

    'cache_store' => env('FIREBASE_CACHE_STORE', 'file'),

    'logging' => [
        'http_debug_log' => env('FIREBASE_HTTP_DEBUG_LOG', false),
        'http_log_channel' => env('FIREBASE_HTTP_LOG_CHANNEL', 'stack'),
    ],

    /*
    |--------------------------------------------------------------------------
    | FCM Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Firebase Cloud Messaging
    |
    */

    'fcm' => [
        'server_key' => env('FCM_SERVER_KEY'),
        'sender_id' => env('FCM_SENDER_ID'),
        'timeout' => env('FCM_TIMEOUT', 30),
        'retry_attempts' => env('FCM_RETRY_ATTEMPTS', 3),
    ],

];
