<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Order;
use App\Models\Blood;
use App\Models\HealthCenter;
use App\Services\NotificationService;
use App\Services\FirebaseService;
use App\Services\SMSService;
use App\Jobs\SendNotificationJob;
use App\Jobs\SendOrderNotificationJob;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;
use Mockery;

class NotificationTest extends TestCase
{
    use RefreshDatabase;

    protected $notificationService;
    protected $firebaseService;
    protected $smsService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the services
        $this->firebaseService = Mockery::mock(FirebaseService::class);
        $this->smsService = Mockery::mock(SMSService::class);
        
        $this->notificationService = new NotificationService(
            $this->firebaseService,
            $this->smsService
        );

        $this->app->instance(NotificationService::class, $this->notificationService);
    }

    /** @test */
    public function user_can_update_fcm_token()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/notifications/fcm-token', [
                'fcm_token' => 'test-fcm-token-123'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'FCM token updated successfully'
            ]);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'fcm' => 'test-fcm-token-123'
        ]);
    }

    /** @test */
    public function user_can_remove_fcm_token()
    {
        $user = User::factory()->create(['fcm' => 'test-token']);
        
        $response = $this->actingAs($user, 'sanctum')
            ->deleteJson('/api/notifications/fcm-token');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'FCM token removed successfully'
            ]);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'fcm' => null
        ]);
    }

    /** @test */
    public function user_can_send_test_notification()
    {
        Queue::fake();

        $user = User::factory()->create(['fcm' => 'test-token']);
        
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/notifications/test');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Test notification sent successfully'
            ]);

        Queue::assertPushed(SendNotificationJob::class);
    }

    /** @test */
    public function user_without_fcm_token_cannot_send_test_notification()
    {
        $user = User::factory()->create(['fcm' => null]);
        
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/notifications/test');

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'FCM token not found for this user'
            ]);
    }

    /** @test */
    public function admin_can_send_custom_notification()
    {
        Queue::fake();

        $admin = User::factory()->create(['is_admin' => true]);
        $users = User::factory()->count(3)->create();
        
        $response = $this->actingAs($admin, 'sanctum')
            ->postJson('/api/notifications/custom', [
                'title' => 'إشعار مخصص',
                'message' => 'هذا إشعار مخصص للاختبار',
                'user_ids' => $users->pluck('id')->toArray(),
                'channels' => ['push']
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Custom notification queued successfully'
            ]);

        Queue::assertPushed(\App\Jobs\SendBulkNotificationJob::class);
    }

    /** @test */
    public function non_admin_cannot_send_custom_notification()
    {
        $user = User::factory()->create(['is_admin' => false]);
        
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/notifications/custom', [
                'title' => 'إشعار مخصص',
                'message' => 'هذا إشعار مخصص للاختبار'
            ]);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ]);
    }

    /** @test */
    public function notification_is_sent_when_order_is_created()
    {
        Queue::fake();

        $blood = Blood::factory()->create();
        $healthCenter = HealthCenter::factory()->create();
        $requester = User::factory()->create();
        $donor = User::factory()->create(['fcm' => 'donor-token']);

        $response = $this->actingAs($requester, 'sanctum')
            ->postJson('/api/orders', [
                'message' => 'أحتاج للتبرع بالدم',
                'to_user_id' => $donor->id,
                'blood_id' => $blood->id,
                'health_center_id' => $healthCenter->id,
                'date' => now()->addDay()->toDateTimeString()
            ]);

        $response->assertStatus(201);
        Queue::assertPushed(SendOrderNotificationJob::class);
    }

    /** @test */
    public function notification_is_sent_when_order_status_is_updated()
    {
        Queue::fake();

        $blood = Blood::factory()->create();
        $healthCenter = HealthCenter::factory()->create();
        $requester = User::factory()->create();
        $donor = User::factory()->create();

        $order = Order::factory()->create([
            'from_user_id' => $requester->id,
            'to_user_id' => $donor->id,
            'blood_id' => $blood->id,
            'health_center_id' => $healthCenter->id,
            'status' => 'pending'
        ]);

        $response = $this->actingAs($donor, 'sanctum')
            ->putJson("/api/orders/{$order->id}", [
                'status' => 'accepted'
            ]);

        $response->assertStatus(200);
        Queue::assertPushed(SendOrderNotificationJob::class);
    }

    /** @test */
    public function user_can_get_notification_preferences()
    {
        $blood = Blood::factory()->create();
        $user = User::factory()->create([
            'fcm' => 'test-token',
            'phone' => '+966501234567',
            'is_donor' => true,
            'blood_id' => $blood->id
        ]);
        
        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/notifications/preferences');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'fcm_token_exists' => true,
                    'phone_exists' => true,
                    'is_donor' => true,
                    'blood_type' => $blood->name,
                    'subscribed_to_blood_type_topic' => true
                ]
            ]);
    }

    /** @test */
    public function firebase_service_can_send_notification()
    {
        $this->firebaseService
            ->shouldReceive('sendToDevice')
            ->once()
            ->with('test-token', 'Test Title', 'Test Message', [], Mockery::any())
            ->andReturn(true);

        $user = User::factory()->create(['fcm' => 'test-token']);

        $result = $this->notificationService->sendToUser(
            $user,
            'Test Title',
            'Test Message',
            [],
            ['push']
        );

        $this->assertTrue($result['push']);
    }

    /** @test */
    public function sms_service_can_send_message()
    {
        $this->smsService
            ->shouldReceive('formatPhoneNumber')
            ->once()
            ->with('+966501234567')
            ->andReturn('+966501234567');

        $this->smsService
            ->shouldReceive('send')
            ->once()
            ->with('+966501234567', Mockery::any())
            ->andReturn(true);

        $user = User::factory()->create(['phone' => '+966501234567']);

        $result = $this->notificationService->sendToUser(
            $user,
            'Test Title',
            'Test Message',
            [],
            ['sms']
        );

        $this->assertTrue($result['sms']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
