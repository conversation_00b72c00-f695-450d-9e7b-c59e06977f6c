# دليل Firebase SMS - مشروع بنك الدم

## نظرة عامة

تم إضافة دعم Firebase SMS إلى نظام الإشعارات في مشروع بنك الدم. يوفر Firebase SMS طريقة موثوقة وآمنة لإرسال رسائل التحقق والرسائل المخصصة.

## المميزات

### 1. التحقق من رقم الهاتف
- إرسال رموز التحقق عبر SMS
- التحقق من صحة الرموز
- دعم reCAPTCHA لمنع الإساءة
- تنسيق أرقام الهواتف تلقائياً

### 2. الرسائل المخصصة
- إرسال رسائل SMS مخصصة (للمدراء)
- دعم Cloud Functions للرسائل المتقدمة
- تسجيل شامل للعمليات

### 3. إدارة متقدمة
- فحص حالة الخدمة
- معلومات تفصيلية عن الرسائل
- تنسيق وتحقق من أرقام الهواتف

## الإعداد

### 1. إعداد Firebase Console

#### تفعيل Phone Authentication
1. اذهب إلى Firebase Console
2. اختر مشروعك
3. اذهب إلى Authentication > Sign-in method
4. فعّل Phone provider
5. أضف أرقام الاختبار إذا لزم الأمر

#### الحصول على Web API Key
1. اذهب إلى Project Settings
2. في تبويب General، انسخ Web API Key
3. أضفه إلى متغيرات البيئة

### 2. متغيرات البيئة

أضف هذه المتغيرات إلى ملف `.env`:

```env
# تغيير مزود SMS إلى Firebase
SMS_PROVIDER=firebase

# إعدادات Firebase SMS
FIREBASE_SERVER_KEY=your-firebase-server-key
FIREBASE_SENDER_ID=your-firebase-sender-id
FIREBASE_WEB_API_KEY=your-firebase-web-api-key
FIREBASE_CLOUD_FUNCTION_URL=https://your-region-your-project.cloudfunctions.net/sendCustomSMS
```

### 3. Cloud Function للرسائل المخصصة (اختياري)

إذا كنت تريد إرسال رسائل SMS مخصصة، أنشئ Cloud Function:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

exports.sendCustomSMS = functions.https.onRequest(async (req, res) => {
  try {
    const { phoneNumber, message, data } = req.body;
    
    // هنا يمكنك استخدام خدمة SMS خارجية مثل Twilio
    // أو أي مزود آخر لإرسال الرسائل المخصصة
    
    // مثال باستخدام Twilio
    const twilio = require('twilio');
    const client = twilio(process.env.TWILIO_SID, process.env.TWILIO_TOKEN);
    
    await client.messages.create({
      body: message,
      from: process.env.TWILIO_FROM,
      to: phoneNumber
    });
    
    res.json({ success: true, message: 'SMS sent successfully' });
  } catch (error) {
    console.error('Error sending SMS:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});
```

## استخدام API

### 1. إرسال رمز التحقق

```http
POST /api/firebase-sms/send-verification
Content-Type: application/json

{
  "phone_number": "+966501234567",
  "recaptcha_token": "optional-recaptcha-token"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "Verification code sent successfully",
  "session_info": "session-info-token"
}
```

### 2. التحقق من رمز التحقق

```http
POST /api/firebase-sms/verify-phone
Content-Type: application/json

{
  "session_info": "session-info-token",
  "code": "123456"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "Phone number verified successfully",
  "id_token": "firebase-id-token",
  "phone_number": "+966501234567"
}
```

### 3. فحص حالة الخدمة

```http
GET /api/firebase-sms/status
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "firebase_sms_available": true,
    "current_sms_provider": "firebase",
    "firebase_configured": true
  }
}
```

### 4. تنسيق رقم الهاتف

```http
POST /api/firebase-sms/format-phone
Content-Type: application/json

{
  "phone_number": "**********",
  "country_code": "+966"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "original": "**********",
    "formatted": "+966501234567",
    "is_valid": true,
    "country_code": "+966"
  }
}
```

### 5. إرسال رسالة مخصصة (للمدراء فقط)

```http
POST /api/firebase-sms/send-custom
Authorization: Bearer your-admin-token
Content-Type: application/json

{
  "phone_number": "+966501234567",
  "message": "رسالة مخصصة من بنك الدم",
  "data": {
    "type": "custom",
    "priority": "high"
  }
}
```

### 6. معلومات الرسالة

```http
POST /api/firebase-sms/message-info
Authorization: Bearer your-token
Content-Type: application/json

{
  "message": "رسالة تجريبية"
}
```

## الاستخدام في الكود

### من Controller

```php
use App\Services\SMSService;

class YourController extends Controller
{
    protected $smsService;

    public function __construct(SMSService $smsService)
    {
        $this->smsService = $smsService;
    }

    public function sendVerification(Request $request)
    {
        if ($this->smsService->isFirebaseAvailable()) {
            $result = $this->smsService->sendFirebaseVerificationCode(
                $request->phone_number
            );
            
            return response()->json($result);
        }
        
        return response()->json(['error' => 'Firebase SMS not available']);
    }
}
```

### من Service

```php
use App\Services\FirebaseSMSService;

$firebaseSMS = new FirebaseSMSService();

// إرسال رمز تحقق
$result = $firebaseSMS->sendVerificationCode('+966501234567');

// التحقق من الرمز
$verification = $firebaseSMS->verifyPhoneNumber($sessionInfo, '123456');

// إرسال رسالة مخصصة
$customSMS = $firebaseSMS->sendCustomSMS('+966501234567', 'رسالة مخصصة');
```

## الأمان

### 1. reCAPTCHA
- استخدم reCAPTCHA لمنع الإساءة
- فعّل reCAPTCHA في Firebase Console
- أرسل reCAPTCHA token مع طلبات التحقق

### 2. Rate Limiting
- Firebase يطبق حدود على عدد الرسائل
- راقب الاستخدام في Firebase Console
- طبق rate limiting إضافي في التطبيق

### 3. أرقام الاختبار
- أضف أرقام اختبار في Firebase Console
- استخدمها أثناء التطوير
- احذفها في الإنتاج

## استكشاف الأخطاء

### 1. خطأ في إرسال رمز التحقق
```
Error: Invalid phone number format
```
**الحل:** تأكد من تنسيق الرقم بصيغة E.164 (+966501234567)

### 2. خطأ في التحقق
```
Error: Invalid verification code
```
**الحل:** تأكد من صحة الرمز وأنه لم ينته صلاحيته

### 3. خطأ في التكوين
```
Error: Firebase Web API Key not configured
```
**الحل:** تأكد من إضافة FIREBASE_WEB_API_KEY في .env

### 4. خطأ في الصلاحيات
```
Error: Unauthorized. Admin access required.
```
**الحل:** تأكد من أن المستخدم لديه صلاحية is_admin = true

## المراقبة والتسجيل

### 1. Logs
- جميع العمليات تُسجل في Laravel logs
- راجع `storage/logs/laravel.log`
- استخدم Log::info() للمعلومات العامة

### 2. Firebase Console
- راقب استخدام Authentication
- تحقق من إحصائيات الرسائل
- راجع الأخطاء والتحذيرات

### 3. Metrics
- عدد رسائل التحقق المرسلة
- معدل نجاح التحقق
- أخطاء الإرسال

## الحدود والقيود

### 1. حدود Firebase
- 10 رسائل/دقيقة لكل رقم هاتف
- 100 رسالة/ساعة لكل مشروع (الخطة المجانية)
- راجع Firebase pricing للحدود المحدثة

### 2. الدول المدعومة
- Firebase يدعم معظم الدول
- بعض الدول قد تحتاج إعداد خاص
- راجع Firebase documentation للقائمة الكاملة

### 3. أنواع الرسائل
- رسائل التحقق فقط مدعومة مباشرة
- الرسائل المخصصة تحتاج Cloud Function
- لا يدعم الرسائل الجماعية مباشرة

## الخلاصة

Firebase SMS يوفر حلاً موثوقاً وآمناً لإرسال رسائل التحقق في تطبيق بنك الدم. مع الإعداد الصحيح، يمكن استخدامه بسهولة لتحسين أمان التطبيق وتجربة المستخدم.

للمساعدة الإضافية، راجع:
- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)
- [Firebase Phone Authentication](https://firebase.google.com/docs/auth/web/phone-auth)
- [Firebase Console](https://console.firebase.google.com)
