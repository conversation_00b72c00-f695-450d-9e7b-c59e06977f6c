<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Blood;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BloodController extends Controller
{
    /**
     * Display a listing of blood types
     */
    public function index()
    {
        $bloodTypes = Blood::where('status', true)->get();

        return response()->json([
            'success' => true,
            'data' => $bloodTypes
        ]);
    }

    /**
     * Store a newly created blood type (Admin only)
     */
    public function store(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:blood',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $blood = Blood::create([
            'name' => $request->name,
            'status' => $request->status ?? true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Blood type created successfully',
            'data' => $blood
        ], 201);
    }

    /**
     * Display the specified blood type
     */
    public function show(Blood $blood)
    {
        return response()->json([
            'success' => true,
            'data' => $blood->load(['users' => function($query) {
                $query->where('status', true)->where('is_donor', true);
            }])
        ]);
    }

    /**
     * Update the specified blood type (Admin only)
     */
    public function update(Request $request, Blood $blood)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:blood,name,' . $blood->id,
            'status' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $blood->update($request->only(['name', 'status']));

        return response()->json([
            'success' => true,
            'message' => 'Blood type updated successfully',
            'data' => $blood
        ]);
    }

    /**
     * Remove the specified blood type (Admin only)
     */
    public function destroy(Request $request, Blood $blood)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Check if blood type is being used by users
        if ($blood->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete blood type. It is being used by users.'
            ], 400);
        }

        $blood->delete();

        return response()->json([
            'success' => true,
            'message' => 'Blood type deleted successfully'
        ]);
    }

    /**
     * Get donors by blood type
     */
    public function donors(Blood $blood)
    {
        $donors = $blood->users()
            ->where('status', true)
            ->where('is_donor', true)
            ->with(['healthCenters', 'diseases'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'blood_type' => $blood,
                'donors' => $donors,
                'total_donors' => $donors->count()
            ]
        ]);
    }
}
