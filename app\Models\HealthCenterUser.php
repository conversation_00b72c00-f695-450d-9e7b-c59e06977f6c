<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HealthCenterUser extends Model
{
    use HasFactory;

    protected $table = 'health_center_users';

    protected $fillable = [
        'health_center_id',
        'user_id',
    ];

    /**
     * Get the health center associated with this pivot record
     */
    public function healthCenter()
    {
        return $this->belongsTo(HealthCenter::class);
    }

    /**
     * Get the user associated with this pivot record
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
