<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\BloodController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\HealthCenterController;
use App\Http\Controllers\Api\StateController;
use App\Http\Controllers\Api\CityController;
use App\Http\Controllers\Api\DiseaseController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\FirebaseSMSController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/verify-account', [AuthController::class, 'verifyAccount']);
Route::post('/resend-verification-code', [AuthController::class, 'resendVerificationCode']);

// Public Firebase SMS routes (for phone verification)
Route::post('/firebase-sms/send-verification', [FirebaseSMSController::class, 'sendVerificationCode']);
Route::post('/firebase-sms/verify-phone', [FirebaseSMSController::class, 'verifyPhoneNumber']);
Route::get('/firebase-sms/status', [FirebaseSMSController::class, 'getServiceStatus']);
Route::post('/firebase-sms/format-phone', [FirebaseSMSController::class, 'formatPhoneNumber']);

// Public data (for registration and general use)
Route::get('/blood-types', [BloodController::class, 'index']);
Route::get('/get/states', [StateController::class, 'index']);
Route::get('/get/cities', [CityController::class, 'index']);
Route::get('/get/diseases', [DiseaseController::class, 'index']);
Route::get('/report-reasons', [ReportController::class, 'reasons']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/profile', [AuthController::class, 'profile']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);

    // Blood routes
    Route::apiResource('blood', BloodController::class);
    Route::get('/blood/{blood}/donors', [BloodController::class, 'donors']);

    // Order routes
    Route::apiResource('orders', OrderController::class);
    Route::get('/orders/sent/list', [OrderController::class, 'sent']);
    Route::get('/orders/received/list', [OrderController::class, 'received']);

    // Health Center routes
    Route::apiResource('health-centers', HealthCenterController::class);
    Route::get('/health-centers/managed/list', [HealthCenterController::class, 'managed']);
    Route::get('/health-centers/associated/list', [HealthCenterController::class, 'associated']);

    // State routes
    Route::apiResource('states', StateController::class);
    Route::get('/states/{state}/cities', [StateController::class, 'cities']);

    // City routes
    Route::apiResource('cities', CityController::class);
    Route::get('/cities/{city}/health-centers', [CityController::class, 'healthCenters']);

    // Disease routes
    Route::apiResource('diseases', DiseaseController::class);
    Route::post('/diseases/{disease}/associate', [DiseaseController::class, 'associate']);
    Route::delete('/diseases/{disease}/disassociate', [DiseaseController::class, 'disassociate']);
    Route::get('/user/diseases', [DiseaseController::class, 'userDiseases']);

    // Report routes
    Route::apiResource('reports', ReportController::class)->only(['index', 'store', 'show', 'destroy']);
    Route::get('/my-reports', [ReportController::class, 'myReports']);

    // Report reasons management (Admin only)
    Route::post('/report-reasons', [ReportController::class, 'storeReason']);
    Route::put('/report-reasons/{reason}', [ReportController::class, 'updateReason']);
    Route::delete('/report-reasons/{reason}', [ReportController::class, 'destroyReason']);

    // Notification routes
    Route::post('/notifications/fcm-token', [NotificationController::class, 'updateFcmToken']);
    Route::delete('/notifications/fcm-token', [NotificationController::class, 'removeFcmToken']);
    Route::post('/notifications/test', [NotificationController::class, 'sendTestNotification']);
    Route::post('/notifications/emergency-blood-request', [NotificationController::class, 'sendEmergencyBloodRequest']);
    Route::post('/notifications/custom', [NotificationController::class, 'sendCustomNotification']); // Admin only
    Route::get('/notifications/preferences', [NotificationController::class, 'getNotificationPreferences']);
    Route::post('/notifications/subscribe-blood-type', [NotificationController::class, 'subscribeToBloodTypeNotifications']);
    Route::post('/notifications/unsubscribe-blood-type', [NotificationController::class, 'unsubscribeFromBloodTypeNotifications']);

    // Firebase SMS routes (authenticated)
    Route::post('/firebase-sms/send-custom', [FirebaseSMSController::class, 'sendCustomSMS']); // Admin only
    Route::post('/firebase-sms/message-info', [FirebaseSMSController::class, 'getMessageInfo']);

    // User info route
    Route::get('/user', function (Request $request) {
        return response()->json([
            'success' => true,
            'data' => $request->user()->load('blood')
        ]);
    });
});
