<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\NotificationService;
use App\Jobs\SendNotificationJob;
use App\Jobs\SendBulkNotificationJob;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Update FCM token for the authenticated user
     */
    public function updateFcmToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fcm_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $user->update(['fcm' => $request->fcm_token]);

        // Subscribe to blood type topic if user is a donor
        if ($user->is_donor && $user->blood) {
            $this->notificationService->subscribeToBloodTypeTopic($user);
        }

        return response()->json([
            'success' => true,
            'message' => 'FCM token updated successfully'
        ]);
    }

    /**
     * Remove FCM token for the authenticated user
     */
    public function removeFcmToken(Request $request)
    {
        $user = $request->user();
        
        // Unsubscribe from blood type topic if subscribed
        if ($user->fcm && $user->is_donor && $user->blood) {
            $this->notificationService->unsubscribeFromBloodTypeTopic($user);
        }

        $user->update(['fcm' => null]);

        return response()->json([
            'success' => true,
            'message' => 'FCM token removed successfully'
        ]);
    }

    /**
     * Send test notification to the authenticated user
     */
    public function sendTestNotification(Request $request)
    {
        $user = $request->user();

        if (!$user->fcm) {
            return response()->json([
                'success' => false,
                'message' => 'FCM token not found for this user'
            ], 400);
        }

        $title = 'إشعار تجريبي';
        $message = 'هذا إشعار تجريبي من تطبيق بنك الدم';
        $data = [
            'type' => 'test_notification',
            'timestamp' => now()->toISOString()
        ];

        SendNotificationJob::dispatch($user->id, $title, $message, $data, ['push']);

        return response()->json([
            'success' => true,
            'message' => 'Test notification sent successfully'
        ]);
    }

    /**
     * Send emergency blood request notification
     */
    public function sendEmergencyBloodRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'blood_type' => 'required|string',
            'location' => 'required|string',
            'contact_info' => 'required|string',
            'channels' => 'sometimes|array',
            'channels.*' => 'in:push,sms'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $channels = $request->get('channels', ['push', 'sms']);

        $result = $this->notificationService->sendEmergencyBloodRequest(
            $request->blood_type,
            $request->location,
            $request->contact_info,
            $channels
        );

        return response()->json([
            'success' => true,
            'message' => 'Emergency blood request sent successfully',
            'data' => [
                'sent_count' => count($result),
                'channels' => $channels
            ]
        ]);
    }

    /**
     * Send custom notification to specific users (Admin only)
     */
    public function sendCustomNotification(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'user_ids' => 'sometimes|array',
            'user_ids.*' => 'exists:users,id',
            'blood_type' => 'sometimes|string',
            'is_donor' => 'sometimes|boolean',
            'channels' => 'sometimes|array',
            'channels.*' => 'in:push,sms',
            'data' => 'sometimes|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $channels = $request->get('channels', ['push']);
        $data = $request->get('data', []);
        $data['type'] = 'custom_notification';
        $data['sent_by_admin'] = true;

        // Get target users
        if ($request->has('user_ids')) {
            $userIds = $request->user_ids;
        } else {
            // Build query based on filters
            $query = User::query();
            
            if ($request->has('blood_type')) {
                $query->whereHas('blood', function($q) use ($request) {
                    $q->where('name', $request->blood_type);
                });
            }
            
            if ($request->has('is_donor')) {
                $query->where('is_donor', $request->is_donor);
            }
            
            $userIds = $query->pluck('id')->toArray();
        }

        if (empty($userIds)) {
            return response()->json([
                'success' => false,
                'message' => 'No users found matching the criteria'
            ], 400);
        }

        // Send bulk notification job
        SendBulkNotificationJob::dispatch(
            $userIds,
            $request->title,
            $request->message,
            $data,
            $channels
        );

        return response()->json([
            'success' => true,
            'message' => 'Custom notification queued successfully',
            'data' => [
                'target_users_count' => count($userIds),
                'channels' => $channels
            ]
        ]);
    }

    /**
     * Get notification preferences for the authenticated user
     */
    public function getNotificationPreferences(Request $request)
    {
        $user = $request->user();

        return response()->json([
            'success' => true,
            'data' => [
                'fcm_token_exists' => !empty($user->fcm),
                'phone_exists' => !empty($user->phone),
                'is_donor' => $user->is_donor,
                'blood_type' => $user->blood->name ?? null,
                'subscribed_to_blood_type_topic' => $user->is_donor && !empty($user->fcm) && $user->blood
            ]
        ]);
    }

    /**
     * Subscribe to blood type notifications
     */
    public function subscribeToBloodTypeNotifications(Request $request)
    {
        $user = $request->user();

        if (!$user->fcm) {
            return response()->json([
                'success' => false,
                'message' => 'FCM token required for subscription'
            ], 400);
        }

        if (!$user->is_donor || !$user->blood) {
            return response()->json([
                'success' => false,
                'message' => 'Only donors with blood type can subscribe'
            ], 400);
        }

        $result = $this->notificationService->subscribeToBloodTypeTopic($user);

        return response()->json([
            'success' => $result,
            'message' => $result ? 'Subscribed successfully' : 'Subscription failed'
        ]);
    }

    /**
     * Unsubscribe from blood type notifications
     */
    public function unsubscribeFromBloodTypeNotifications(Request $request)
    {
        $user = $request->user();

        if (!$user->fcm || !$user->blood) {
            return response()->json([
                'success' => false,
                'message' => 'No active subscription found'
            ], 400);
        }

        $result = $this->notificationService->unsubscribeFromBloodTypeTopic($user);

        return response()->json([
            'success' => $result,
            'message' => $result ? 'Unsubscribed successfully' : 'Unsubscription failed'
        ]);
    }
}
