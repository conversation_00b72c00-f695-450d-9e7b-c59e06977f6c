<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\SMSService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class AuthController extends Controller
{
    protected $smsService;

    public function __construct(SMSService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Register a new user
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'first_contact_phone' => 'nullable|string|max:20|unique:users',
            'second_contact_phone' => 'nullable|string|max:20|unique:users',
            'gender' => 'required|boolean',
            'birthdate' => 'required|date',
            'blood_id' => 'required|exists:blood,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Generate verification code
        $verificationCode = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $expirationTime = Carbon::now()->addMinutes(10); // Code expires in 10 minutes

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'first_contact_phone' => $request->first_contact_phone,
            'second_contact_phone' => $request->second_contact_phone,
            'gender' => $request->gender,
            'birthdate' => $request->birthdate,
            'blood_id' => $request->blood_id,
            'status' => false, // Needs verification
            'verification_code' => $verificationCode,
            'verification_code_expired_at' => $expirationTime,
        ]);

        // Send verification code via SMS
        $message = "كود التحقق الخاص بك في تطبيق بنك الدم هو: {$verificationCode}. صالح لمدة 10 دقائق.";
        $this->smsService->send($user->phone, $message);

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'User registered successfully. Verification code sent to your phone.',
        ], 200);
    }

    /**
     * Login user
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|exists:users,phone',
            'password' => 'required|string',
            'fcm' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        if (!Auth::attempt($request->only('phone', 'password'))) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        $user = User::where('phone', $request->phone)->firstOrFail();
        $token = $user->createToken('auth_token')->plainTextToken;
        $user->update(['fcm' => $request->fcm]);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user->load('blood'),
                'token' => $token,
                'token_type' => 'Bearer'
            ]
        ],200);
    }

    /**
     * Verify user account with SMS code
     */
    public function verifyAccount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|exists:users,phone',
            'verification_code' => 'required|string|size:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('phone', $request->phone)->first();

        // Check if user exists
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        // Check if user is already verified
        if ($user->status) {
            return response()->json([
                'success' => false,
                'message' => 'Account is already verified'
            ], 400);
        }

        // Check if verification code matches
        if ($user->verification_code !== $request->verification_code) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid verification code'
            ], 400);
        }

        // Check if verification code is expired
        if (Carbon::now()->isAfter($user->verification_code_expired_at)) {
            return response()->json([
                'success' => false,
                'message' => 'Verification code has expired'
            ], 400);
        }

        // Verify the user account
        $user->update([
            'status' => true,
            'verification_code' => null,
            'verification_code_expired_at' => null,
        ]);
        $token = $user->createToken('auth_token')->plainTextToken;


        return response()->json([
            'success' => true,
            'message' => 'Account verified successfully',
            'data' => [
                'user' => $user->load('blood'),
                'token' => $token,
                'token_type' => 'Bearer'
            ]
        ]);
    }

    /**
     * Resend verification code
     */
    public function resendVerificationCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|exists:users,phone'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('phone', $request->phone)->first();

        // Check if user exists
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        // Check if user is already verified
        if ($user->status) {
            return response()->json([
                'success' => false,
                'message' => 'Account is already verified'
            ], 400);
        }

        // Generate new verification code
        $verificationCode = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $expirationTime = Carbon::now()->addMinutes(10);

        // Update user with new verification code
        $user->update([
            'verification_code' => $verificationCode,
            'verification_code_expired_at' => $expirationTime,
        ]);

        // Send verification code via SMS
        $message = "كود التحقق الجديد الخاص بك في تطبيق بنك الدم هو: {$verificationCode}. صالح لمدة 10 دقائق.";
        $this->smsService->send($user->phone, $message);

        return response()->json([
            'success' => true,
            'message' => 'New verification code sent to your phone'
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        $request->user()->update(['fcm' => null]);

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Get user profile
     */
    public function profile(Request $request)
    {
        return response()->json([
            'success' => true,
            'data' => [
                'user' => $request->user()->load(['blood', 'diseases', 'healthCenters'])
            ]
        ]);
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|string|max:20|unique:users,phone,' . $user->id,
            'first_contact_phone' => 'sometimes|string|max:20|unique:users,first_contact_phone,' . $user->id,
            'second_contact_phone' => 'sometimes|string|max:20|unique:users,second_contact_phone,' . $user->id,
            'gender' => 'sometimes|boolean',
            'birthdate' => 'sometimes|date',
            'blood_id' => 'sometimes|exists:blood,id',
            'is_donor' => 'sometimes|boolean',
            'more_info' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($request->only([
            'name', 'phone', 'first_contact_phone', 'second_contact_phone',
            'gender', 'birthdate', 'blood_id', 'is_donor', 'more_info'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => [
                'user' => $user->fresh()->load('blood')
            ]
        ]);
    }
}
