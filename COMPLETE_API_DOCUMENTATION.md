# APIs مشروع بنك الدم

## المصادقة (Authentication)

### Headers المطلوبة للـ Protected Routes:
`````````````````````````````````
Authorization: Bearer {your_api_token}
Content-Type: application/json
Accept: application/json
`````````````````````````````````

### ------------ Authentication APIs
### 1. تسجيل مستخدم جديد
```http
POST /api/register
```
**Headers:**
```
Content-Type: application/json
Accept: application/json
```
**Body:**
```json
{
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "phone": "+************",
    "password": "password123",
    "password_confirmation": "password123",
    "first_contact_phone": "+966501234568",
    "second_contact_phone": "+966501234569",
    "gender": true,
    "birthdate": "1990-01-01",
    "blood_id": 1
}
```

**Validation Rules:**
- `name`: required|string|max:255
- `email`: nullable|email|unique:users
- `phone`: required|string|max:20|unique:users
- `password`: required|string|min:8|confirmed
- `first_contact_phone`: nullable|string|max:20|unique:users
- `second_contact_phone`: nullable|string|max:20|unique:users
- `gender`: required|boolean (true=ذكر, false=أنثى)
- `birthdate`: required|date
- `blood_id`: required|exists:blood,id

**Success Response (200):**
```json
{
    "success": true,
    "message": "User registered successfully. Verification code sent to your phone.",
    
}
```

### 2. تسجيل الدخول
```http
POST /api/login
```
**Body:**
```json
{
    "phone": "<EMAIL>",
    "password": "password123",
    "fcm": "firebase_token_here"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "name": "أحمد محمد",
            "email": "<EMAIL>",
            "phone": "+************",
            "status": true,
            "blood": {
                "id": 1,
                "name": "A+"
            }
        },
        "token": "2|def456...",
        "token_type": "Bearer"
    }
}
```

### 3. التحقق من الحساب
```http
POST /api/verify-account
```
**Body:**
```json
{
    "phone": "+************",
    "verification_code": "123456"
}
```
**Success Response (200):**
```json
{
    "success": true,
    "message": "Account verified successfully",
    "data": {
        "user": {
            "id": 1,
            "name": "أحمد محمد",
            "phone": "+************",
            "status": true,
            "blood": {
                "id": 1,
                "name": "A+"
            }
        },
        "token": "2|def456...",
        "token_type": "Bearer"
    }
}
```

### 4. إعادة إرسال كود التحقق
```http
POST /api/resend-verification-code
```
**Body:**
```json
{
    "phone": "+************"
}
```
**Success Response (200):**
```json
{
    "success": true,
    "message": "New verification code sent to your phone"
}
```

### 5. تسجيل الخروج
```http
POST /api/logout
```
**Headers:**
```
Authorization: Bearer {token}
```
**Success Response (200):**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

### 6. عرض الملف الشخصي
```http
GET /api/profile
```
**Headers:**
```
Authorization: Bearer {token}
```
**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "name": "أحمد محمد",
            "email": "<EMAIL>",
            "phone": "+************",
            "status": true,
            "blood": {
                "id": 1,
                "name": "A+"
            },
            "diseases": [],
            "health_centers": []
        }
    }
}
```

---

## ------------ Public Data APIs
### 1. قائمة المحافظات
```http
GET /api/get/states
```
**Success Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "الرياض"
        },
        {
            "id": 2,
            "name": "مكة المكرمة"
        }
    ]
}
```

### 2. قائمة المدن
```http
GET /api/get/cities
```

### 3. قائمة الأمراض
```http
GET /api/get/diseases
```

### 4. أسباب التقارير
```http
GET /api/report-reasons
```

---

## ------------ Blood Types APIs

### 1. قائمة أنواع الدم (Public)
```http
GET /api/blood-types
```
**Success Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "A+"
        },
        {
            "id": 2,
            "name": "A-"
        }
    ]
}
```

### 2. المتبرعين بنوع دم محدد
```http
GET /api/blood/{blood_id}/donors
```

**Headers:**
```
Authorization: Bearer {token}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "blood_type": {
            "id": 1,
            "name": "A+"
        },
        "donors": [
            {
                "id": 2,
                "name": "محمد أحمد",
                "phone": "+966501234568",
                "health_centers": [],
                "diseases": []
            }
        ],
        "total_donors": 1
    }
}
```

---

## ------------ Orders APIs

### 1. قائمة الطلبات
```http
GET /api/orders
```

**Headers:**
```
Authorization: Bearer {token}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "message": "أحتاج متبرع دم عاجل",
                "status": "pending",
                "date": "2024-01-01 10:00:00",
                "from_user": {
                    "id": 1,
                    "name": "أحمد محمد"
                },
                "to_user": {
                    "id": 2,
                    "name": "محمد أحمد"
                },
                "blood": {
                    "id": 1,
                    "name": "A+"
                },
                "health_center": {
                    "id": 1,
                    "name": "مستشفى الملك فهد"
                }
            }
        ],
        "per_page": 15,
        "total": 1
    }
}
```

### 2. إنشاء طلب جديد
```http
POST /api/orders
```

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body:**
```json
{
    "message": "أحتاج متبرع دم عاجل للطوارئ",
    "to_user_id": 2,
    "blood_id": 1,
    "health_center_id": 1,
    "date": "2024-01-01 10:00:00"
}
```

**Validation Rules:**
- `message`: required|string
- `to_user_id`: required|exists:users,id
- `blood_id`: required|exists:blood,id
- `health_center_id`: required|exists:health_center,id
- `date`: sometimes|date|after:now

**Success Response (201):**
```json
{
    "success": true,
    "message": "Order created successfully",
    "data": {
        "id": 1,
        "message": "أحتاج متبرع دم عاجل للطوارئ",
        "status": "pending",
        "from_user": {
            "id": 1,
            "name": "أحمد محمد"
        },
        "to_user": {
            "id": 2,
            "name": "محمد أحمد"
        },
        "blood": {
            "id": 1,
            "name": "A+"
        },
        "health_center": {
            "id": 1,
            "name": "مستشفى الملك فهد"
        }
    }
}
```

### 3. تحديث حالة الطلب
```http
PUT /api/orders/{order_id}
```

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body:**
```json
{
    "status": "accepted"
}
```

**Validation Rules:**
- `status`: required|in:pending,accepted,rejected,completed

**Success Response (200):**
```json
{
    "success": true,
    "message": "Order status updated successfully",
    "data": {
        "id": 1,
        "status": "accepted"
    }
}
```

### 4. الطلبات المرسلة
```http
GET /api/orders/sent/list
```

### 5. الطلبات المستلمة
```http
GET /api/orders/received/list
```

---

## ------------ Health Centers APIs

### 1. قائمة المراكز الصحية
```http
GET /api/health-centers
```

**Headers:**
```
Authorization: Bearer {token}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "المستشفى الملكي",
            "address": "صنعاء شارع الخمسين بيت بوس",
            "phone": "+967112345678",
            "city": {
                "id": 1,
                "name": "صنعاء"
            }
        }
    ]
}
```

### 2. إنشاء مركز صحي جديد (Admin Only)
```http
POST /api/health-centers
```

**Headers:**
```
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**Body:**
```json
{
    "name": "مستشفى الخمسين",
    "address": "صنعاء شارع الخمسين",
    "phone": "+967112345679",
    "email": "<EMAIL>",
    "image": file,
    "more_info": "",
    "city_id": 1
}
```
**Validation Rules:**
'name' => 'required|string|max:255',
'address' => 'required|string|max:255',
'phone' => 'required|string|max:20',
'email' => 'required|email|max:255',
'city_id' => 'required|exists:cities,id',
'user_id' => 'required|exists:users,id',
'image' => 'sometimes|string',
'more_info' => 'sometimes|string',
'status' => 'boolean',


### 3. المراكز المُدارة
```http
GET /api/health-centers/managed/list
```

### 4. المراكز المرتبطة
```http
GET /api/health-centers/associated/list
```

---

## 🏛️ States & Cities APIs

### 1. قائمة المحافظات مع المدن
```http
GET /api/states
```

**Headers:**
```
Authorization: Bearer {token}
```

### 2. مدن محافظة محددة
```http
GET /api/states/{state_id}/cities
```

### 3. المراكز الصحية في مدينة محددة
```http
GET /api/cities/{city_id}/health-centers
```

---

## 🦠 Diseases APIs

### 1. قائمة الأمراض (Public)
```http
GET /api/get/diseases
```

**Success Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "السكري"
        },
        {
            "id": 2,
            "name": "ضغط الدم"
        }
    ]
}
```

### 2. ربط مرض بالمستخدم
```http
POST /api/diseases/{disease_id}/associate
```

**Headers:**
```
Authorization: Bearer {token}
```

### 3. إلغاء ربط مرض
```http
DELETE /api/diseases/{disease_id}/disassociate
```

---

## 📊 Reports APIs

### 1. إنشاء تقرير
```http
POST /api/reports
```

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body Parameters:**
```json
{
    "reported_user_id": 2,
    "reason_id": 1,
    "description": "سلوك غير لائق"
}
```

### 2. قائمة تقاريري
```http
GET /api/reports/my
```

### 3. أسباب التقارير (Public)
```http
GET /api/report-reasons
```

**Success Response (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "reason": "سلوك غير لائق"
        },
        {
            "id": 2,
            "reason": "محتوى مسيء"
        }
    ]
}
```

---

## 🔔 Notifications APIs

### 1. تحديث FCM Token
```http
PUT /api/notifications/fcm-token
```

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Body Parameters:**
```json
{
    "fcm_token": "firebase_cloud_messaging_token_here"
}
```

### 2. حذف FCM Token
```http
DELETE /api/notifications/fcm-token
```

### 3. إرسال إشعار تجريبي (Admin Only)
```http
POST /api/notifications/test
```

**Headers:**
```
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**Body Parameters:**
```json
{
    "user_id": 2,
    "title": "إشعار تجريبي",
    "body": "هذا إشعار تجريبي من النظام",
    "channels": ["push", "sms"]
}
```

### 4. طلب دم عاجل (Admin Only)
```http
POST /api/notifications/urgent-blood-request
```

**Body Parameters:**
```json
{
    "blood_id": 1,
    "health_center_id": 1,
    "message": "مطلوب متبرعين دم A+ بشكل عاجل",
    "channels": ["push", "sms"]
}
```

### 5. إرسال إشعار مخصص (Admin Only)
```http
POST /api/notifications/custom
```

**Body Parameters:**
```json
{
    "title": "إشعار مهم",
    "body": "رسالة مهمة لجميع المستخدمين",
    "channels": ["push"],
    "target_type": "all"
}
```

### 6. تفضيلات الإشعارات
```http
GET /api/notifications/preferences
```

```http
PUT /api/notifications/preferences
```

**Body Parameters:**
```json
{
    "push_notifications": true,
    "sms_notifications": false,
    "email_notifications": true
}
```

### 7. الاشتراك في إشعارات فصيلة دم
```http
POST /api/notifications/subscribe-blood-type
```

**Body Parameters:**
```json
{
    "blood_id": 1,
    "subscribe": true
}
```

---

## 🔥 Firebase SMS APIs

### 1. إرسال كود التحقق
```http
POST /api/firebase-sms/send-verification
```

**Body Parameters:**
```json
{
    "phone": "+************"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Verification code sent successfully",
    "data": {
        "phone": "+************",
        "expires_in": 300
    }
}
```

### 2. التحقق من رقم الهاتف
```http
POST /api/firebase-sms/verify-phone
```

**Body Parameters:**
```json
{
    "phone": "+************",
    "verification_code": "123456"
}
```

### 3. حالة الخدمة
```http
GET /api/firebase-sms/status
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "service": "Firebase SMS",
        "status": "active",
        "provider": "firebase"
    }
}
```

### 4. تنسيق رقم الهاتف
```http
POST /api/firebase-sms/format-phone
```

**Body Parameters:**
```json
{
    "phone": "**********",
    "country_code": "SA"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "original": "**********",
        "formatted": "+************",
        "country_code": "SA",
        "is_valid": true
    }
}
```

### 5. إرسال رسالة مخصصة (Admin Only)
```http
POST /api/firebase-sms/send-custom
```

**Headers:**
```
Authorization: Bearer {admin_token}
```

**Body Parameters:**
```json
{
    "phone": "+************",
    "message": "رسالة مخصصة من إدارة بنك الدم"
}
```

### 6. معلومات الرسالة
```http
GET /api/firebase-sms/message-info/{message_id}
```

---

## ⚠️ Error Responses

### Validation Error (422):
```json
{
    "success": false,
    "message": "Validation errors",
    "errors": {
        "email": ["The email field is required."],
        "password": ["The password field is required."]
    }
}
```

### Unauthorized (401):
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

### Forbidden (403):
```json
{
    "success": false,
    "message": "Unauthorized. Admin access required."
}
```

### Not Found (404):
```json
{
    "success": false,
    "message": "Resource not found"
}
```

### Server Error (500):
```json
{
    "success": false,
    "message": "Internal server error"
}
```

---

## 📊 Status Codes

- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **500**: Server Error

---

## 🚀 Getting Started

1. **التسجيل**: استخدم `POST /api/register` لإنشاء حساب جديد
2. **التحقق**: استخدم `POST /api/verify-account` للتحقق من رقم الهاتف
3. **تسجيل الدخول**: استخدم `POST /api/login` للحصول على token
4. **استخدام APIs**: أضف `Authorization: Bearer {token}` في headers
5. **الوصول للبيانات**: يمكنك الآن الوصول لجميع الـ Protected APIs

---

## 💡 أمثلة عملية للاستخدام

### مثال 1: تدفق التسجيل الكامل

```javascript
// 1. الحصول على أنواع الدم
const bloodTypes = await fetch('/api/blood-types');

// 2. التسجيل
const registerResponse = await fetch('/api/register', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+************',
        password: 'password123',
        password_confirmation: 'password123',
        gender: true,
        birthdate: '1990-01-01',
        blood_id: 1
    })
});

const registerData = await registerResponse.json();
const token = registerData.data.token;

// 3. التحقق من الحساب
const verifyResponse = await fetch('/api/verify-account', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        phone: '+************',
        verification_code: '123456'
    })
});
```

### مثال 2: إنشاء طلب دم

```javascript
// 1. تسجيل الدخول أولاً
const loginResponse = await fetch('/api/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        fcm: 'firebase_token'
    })
});

const loginData = await loginResponse.json();
const token = loginData.data.token;

// 2. الحصول على المتبرعين
const donorsResponse = await fetch('/api/blood/1/donors', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});

// 3. إنشاء طلب دم
const orderResponse = await fetch('/api/orders', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        message: 'أحتاج متبرع دم عاجل للطوارئ',
        to_user_id: 2,
        blood_id: 1,
        health_center_id: 1,
        date: '2024-01-01 10:00:00'
    })
});
```

### مثال 3: إدارة الإشعارات

```javascript
// تحديث FCM Token
await fetch('/api/notifications/fcm-token', {
    method: 'PUT',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        fcm_token: 'new_firebase_token'
    })
});

// الاشتراك في إشعارات فصيلة دم
await fetch('/api/notifications/subscribe-blood-type', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        blood_id: 1,
        subscribe: true
    })
});
```

---

## 📱 Integration Guide للمطورين

### إعداد Base URL
```javascript
const API_BASE_URL = 'http://your-domain.com/api';
```

### إعداد Axios Interceptor
```javascript
import axios from 'axios';

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

// إضافة token تلقائياً
api.interceptors.request.use((config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// معالجة الأخطاء
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // إعادة توجيه لصفحة تسجيل الدخول
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);
```

### Flutter/Dart Example
```dart
import 'package:http/http.dart' as http;
import 'dart:convert';

class ApiService {
    static const String baseUrl = 'http://your-domain.com/api';
    static String? authToken;

    static Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
        final response = await http.post(
            Uri.parse('$baseUrl/register'),
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: json.encode(userData),
        );

        return json.decode(response.body);
    }

    static Future<Map<String, dynamic>> login(String email, String password) async {
        final response = await http.post(
            Uri.parse('$baseUrl/login'),
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: json.encode({
                'email': email,
                'password': password,
            }),
        );

        final data = json.decode(response.body);
        if (data['success']) {
            authToken = data['data']['token'];
        }
        return data;
    }

    static Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderData) async {
        final response = await http.post(
            Uri.parse('$baseUrl/orders'),
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': 'Bearer $authToken',
            },
            body: json.encode(orderData),
        );

        return json.decode(response.body);
    }
}
```

---

## 🔧 Testing APIs

### استخدام cURL
```bash
# التسجيل
curl -X POST http://your-domain.com/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "phone": "+************",
    "password": "password123",
    "password_confirmation": "password123",
    "gender": true,
    "birthdate": "1990-01-01",
    "blood_id": 1
  }'

# تسجيل الدخول
curl -X POST http://your-domain.com/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# إنشاء طلب (مع token)
curl -X POST http://your-domain.com/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "message": "أحتاج متبرع دم عاجل",
    "to_user_id": 2,
    "blood_id": 1,
    "health_center_id": 1
  }'
```

### استخدام Postman
1. استورد ملف `Blood_Bank_API.postman_collection.json`
2. اضبط متغير `base_url` إلى رابط الخادم
3. سجل دخول واحصل على token
4. اضبط متغير `api_token` بالـ token المحصل عليه
5. ابدأ في اختبار جميع الـ endpoints

---

## 🛡️ Security Notes

- جميع كلمات المرور مُشفرة باستخدام bcrypt
- الـ tokens آمنة ومُدارة بواسطة Laravel Sanctum
- جميع المدخلات مُتحقق منها قبل المعالجة
- حماية من CSRF attacks
- Rate limiting مُفعل لمنع الإساءة
- جميع الـ endpoints الحساسة محمية بـ authentication
- صلاحيات Admin منفصلة ومحمية
- تشفير البيانات الحساسة في قاعدة البيانات

---

## 📞 Support & Contact

للدعم الفني أو الاستفسارات:
- **Email**: <EMAIL>
- **Documentation**: راجع هذا الملف للتفاصيل الكاملة
- **Testing**: استخدم Postman Collection المرفق
