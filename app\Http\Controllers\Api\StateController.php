<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class StateController extends Controller
{
    /**
     * Display a listing of states
     */
    public function index()
    {
        $states = State::where('status', true)
            ->with(['cities' => function($query) {
                $query->where('status', true);
            }])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $states
        ]);
    }

    /**
     * Store a newly created state (Admin only)
     */
    public function store(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:states',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $state = State::create([
            'name' => $request->name,
            'status' => $request->status ?? true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'State created successfully',
            'data' => $state
        ], 201);
    }

    /**
     * Display the specified state
     */
    public function show(State $state)
    {
        return response()->json([
            'success' => true,
            'data' => $state->load(['cities' => function($query) {
                $query->where('status', true);
            }])
        ]);
    }

    /**
     * Update the specified state (Admin only)
     */
    public function update(Request $request, State $state)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:states,name,' . $state->id,
            'status' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $state->update($request->only(['name', 'status']));

        return response()->json([
            'success' => true,
            'message' => 'State updated successfully',
            'data' => $state
        ]);
    }

    /**
     * Remove the specified state (Admin only)
     */
    public function destroy(Request $request, State $state)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Check if state has cities
        if ($state->cities()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete state. It has associated cities.'
            ], 400);
        }

        $state->delete();

        return response()->json([
            'success' => true,
            'message' => 'State deleted successfully'
        ]);
    }

    /**
     * Get cities in the specified state
     */
    public function cities(State $state)
    {
        $cities = $state->cities()->where('status', true)->get();

        return response()->json([
            'success' => true,
            'data' => [
                'state' => $state,
                'cities' => $cities
            ]
        ]);
    }
}
