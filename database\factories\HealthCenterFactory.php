<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\HealthCenter>
 */
class HealthCenterFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' Medical Center',
            'address' => $this->faker->address,
            'phone' => $this->faker->phoneNumber,
            'user_id' => \App\Models\User::factory(),
            'city_id' => 1, // Default city
        ];
    }
}
