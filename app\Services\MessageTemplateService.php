<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;

class MessageTemplateService
{
    /**
     * Get notification templates
     *
     * @return array
     */
    public static function getNotificationTemplates(): array
    {
        return [
            'new_order' => [
                'title' => 'طلب تبرع جديد 🩸',
                'message' => 'مرحباً {to_user_name}، لديك طلب تبرع جديد من {from_user_name} لفصيلة دم {blood_type} في {health_center}. يرجى مراجعة التطبيق للرد على الطلب.',
                'sms' => 'طلب تبرع جديد من {from_user_name} لفصيلة {blood_type} في {health_center}. راجع التطبيق للتفاصيل.'
            ],
            'order_accepted' => [
                'title' => 'تم قبول طلب التبرع ✅',
                'message' => 'مبروك! تم قبول طلب التبرع الخاص بك من قبل {to_user_name}. يرجى التواصل معه لتنسيق موعد التبرع.',
                'sms' => 'تم قبول طلب التبرع من {to_user_name}. تواصل معه لتنسيق الموعد.'
            ],
            'order_rejected' => [
                'title' => 'تم رفض طلب التبرع ❌',
                'message' => 'نأسف، تم رفض طلب التبرع الخاص بك من قبل {to_user_name}. يمكنك البحث عن متبرعين آخرين.',
                'sms' => 'تم رفض طلب التبرع من {to_user_name}. ابحث عن متبرعين آخرين.'
            ],
            'order_completed' => [
                'title' => 'تم إكمال التبرع بنجاح 🎉',
                'message' => 'تم إكمال عملية التبرع بنجاح! شكراً لك على مساهمتك النبيلة في إنقاذ الأرواح. جزاك الله خيراً.',
                'sms' => 'تم إكمال التبرع بنجاح! شكراً لمساهمتك النبيلة.'
            ],
            'emergency_blood_request' => [
                'title' => 'طلب دم عاجل 🚨',
                'message' => 'طلب دم عاجل! مطلوب متبرع بفصيلة دم {blood_type} في {location}. للتواصل: {contact_info}',
                'sms' => 'طلب دم عاجل! فصيلة {blood_type} في {location}. اتصل: {contact_info}'
            ],
            'welcome_donor' => [
                'title' => 'مرحباً بك في بنك الدم 🩸',
                'message' => 'مرحباً {user_name}! شكراً لانضمامك كمتبرع. ستصلك إشعارات عند الحاجة لفصيلة دمك {blood_type}.',
                'sms' => 'مرحباً {user_name}! شكراً لانضمامك كمتبرع في بنك الدم.'
            ],
            'reminder_donation' => [
                'title' => 'تذكير: موعد التبرع 📅',
                'message' => 'تذكير: لديك موعد تبرع اليوم في {health_center} مع {requester_name}. لا تنس الحضور!',
                'sms' => 'تذكير: موعد التبرع اليوم في {health_center} مع {requester_name}.'
            ],
            'thank_you_donation' => [
                'title' => 'شكراً لك على التبرع 🙏',
                'message' => 'شكراً لك {donor_name} على تبرعك النبيل! لقد ساهمت في إنقاذ حياة إنسان. جعله الله في ميزان حسناتك.',
                'sms' => 'شكراً {donor_name} على تبرعك النبيل! جعله الله في ميزان حسناتك.'
            ]
        ];
    }

    /**
     * Get SMS templates
     *
     * @return array
     */
    public static function getSMSTemplates(): array
    {
        return [
            'verification_code' => 'رمز التحقق الخاص بك في بنك الدم: {code}. لا تشارك هذا الرمز مع أحد.',
            'password_reset' => 'رمز إعادة تعيين كلمة المرور: {code}. صالح لمدة 10 دقائق.',
            'account_activated' => 'تم تفعيل حسابك في بنك الدم بنجاح. يمكنك الآن استخدام التطبيق.',
            'account_suspended' => 'تم إيقاف حسابك مؤقتاً. للاستفسار تواصل مع الدعم الفني.',
            'new_message' => 'لديك رسالة جديدة من {sender_name} في بنك الدم.',
            'appointment_confirmed' => 'تم تأكيد موعدك في {health_center} يوم {date} الساعة {time}.',
            'appointment_cancelled' => 'تم إلغاء موعدك في {health_center}. يرجى إعادة الجدولة.',
        ];
    }

    /**
     * Get email templates
     *
     * @return array
     */
    public static function getEmailTemplates(): array
    {
        return [
            'welcome' => [
                'subject' => 'مرحباً بك في بنك الدم',
                'body' => 'مرحباً {user_name}،\n\nنرحب بك في منصة بنك الدم. شكراً لانضمامك إلى مجتمعنا النبيل من المتبرعين.\n\nمع أطيب التحيات،\nفريق بنك الدم'
            ],
            'order_summary' => [
                'subject' => 'ملخص طلب التبرع',
                'body' => 'عزيزي {user_name}،\n\nإليك ملخص طلب التبرع:\n- فصيلة الدم: {blood_type}\n- المركز الصحي: {health_center}\n- التاريخ: {date}\n- الحالة: {status}\n\nشكراً لك.'
            ],
            'monthly_report' => [
                'subject' => 'التقرير الشهري - بنك الدم',
                'body' => 'عزيزي {user_name}،\n\nإليك تقريرك الشهري:\n- عدد التبرعات: {donations_count}\n- آخر تبرع: {last_donation_date}\n- النقاط المكتسبة: {points}\n\nشكراً لمساهمتك المستمرة.'
            ]
        ];
    }

    /**
     * Replace placeholders in template
     *
     * @param string $template
     * @param array $data
     * @return string
     */
    public static function replacePlaceholders(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{' . $key . '}', $value, $template);
        }
        return $template;
    }

    /**
     * Get order notification data
     *
     * @param Order $order
     * @return array
     */
    public static function getOrderNotificationData(Order $order): array
    {
        return [
            'from_user_name' => $order->fromUser->name,
            'to_user_name' => $order->toUser->name,
            'blood_type' => $order->blood->name ?? 'غير محدد',
            'health_center' => $order->healthCenter->name ?? 'غير محدد',
            'order_id' => $order->id,
            'date' => $order->date ? $order->date->format('Y-m-d H:i') : 'غير محدد',
            'status' => $order->status,
            'message' => $order->message,
        ];
    }

    /**
     * Get user notification data
     *
     * @param User $user
     * @return array
     */
    public static function getUserNotificationData(User $user): array
    {
        return [
            'user_name' => $user->name,
            'user_id' => $user->id,
            'blood_type' => $user->blood->name ?? 'غير محدد',
            'phone' => $user->phone,
            'email' => $user->email,
            'is_donor' => $user->is_donor ? 'نعم' : 'لا',
        ];
    }

    /**
     * Format notification for specific channel
     *
     * @param string $templateKey
     * @param string $channel
     * @param array $data
     * @return array
     */
    public static function formatNotification(string $templateKey, string $channel, array $data): array
    {
        $templates = self::getNotificationTemplates();
        
        if (!isset($templates[$templateKey])) {
            throw new \InvalidArgumentException("Template '{$templateKey}' not found");
        }

        $template = $templates[$templateKey];

        switch ($channel) {
            case 'push':
                return [
                    'title' => self::replacePlaceholders($template['title'], $data),
                    'body' => self::replacePlaceholders($template['message'], $data)
                ];
            
            case 'sms':
                $smsTemplate = $template['sms'] ?? $template['message'];
                return [
                    'message' => self::replacePlaceholders($smsTemplate, $data)
                ];
            
            case 'email':
                return [
                    'subject' => self::replacePlaceholders($template['title'], $data),
                    'body' => self::replacePlaceholders($template['message'], $data)
                ];
            
            default:
                throw new \InvalidArgumentException("Unsupported channel: {$channel}");
        }
    }

    /**
     * Get template by key and type
     *
     * @param string $key
     * @param string $type
     * @return string|array|null
     */
    public static function getTemplate(string $key, string $type = 'notification')
    {
        switch ($type) {
            case 'notification':
                $templates = self::getNotificationTemplates();
                break;
            case 'sms':
                $templates = self::getSMSTemplates();
                break;
            case 'email':
                $templates = self::getEmailTemplates();
                break;
            default:
                return null;
        }

        return $templates[$key] ?? null;
    }

    /**
     * Validate template data
     *
     * @param string $template
     * @param array $data
     * @return array Missing placeholders
     */
    public static function validateTemplateData(string $template, array $data): array
    {
        preg_match_all('/\{([^}]+)\}/', $template, $matches);
        $placeholders = $matches[1];
        $missing = [];

        foreach ($placeholders as $placeholder) {
            if (!array_key_exists($placeholder, $data)) {
                $missing[] = $placeholder;
            }
        }

        return $missing;
    }
}
