<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Exception;

class NotificationService
{
    protected $firebaseService;
    protected $smsService;

    public function __construct(FirebaseService $firebaseService, SMSService $smsService)
    {
        $this->firebaseService = $firebaseService;
        $this->smsService = $smsService;
    }

    /**
     * Send notification to user via multiple channels
     *
     * @param User $user
     * @param string $title
     * @param string $message
     * @param array $data
     * @param array $channels
     * @return array
     */
    public function sendToUser(User $user, string $title, string $message, array $data = [], array $channels = ['push', 'sms']): array
    {
        $results = [];

        try {
            // Send push notification if FCM token exists and push channel is enabled
            if (in_array('push', $channels) && $user->fcm) {
                $results['push'] = $this->firebaseService->sendToDevice(
                    $user->fcm,
                    $title,
                    $message,
                    $data,
                    $this->getDefaultPushOptions()
                );
            }

            // Send SMS if phone exists and SMS channel is enabled
            if (in_array('sms', $channels) && $user->phone) {
                $smsMessage = $this->formatSMSMessage($title, $message);
                $formattedPhone = $this->smsService->formatPhoneNumber($user->phone);
                $results['sms'] = $this->smsService->send($formattedPhone, $smsMessage);
            }

            Log::info('Notification sent to user', [
                'user_id' => $user->id,
                'title' => $title,
                'channels' => $channels,
                'results' => $results
            ]);

        } catch (Exception $e) {
            Log::error('Failed to send notification to user: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'title' => $title,
                'error' => $e->getMessage()
            ]);
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Send order notification
     *
     * @param Order $order
     * @param string $type
     * @param array $channels
     * @return array
     */
    public function sendOrderNotification(Order $order, string $type, array $channels = ['push', 'sms']): array
    {
        $recipient = $type === 'new_order' ? $order->toUser : $order->fromUser;
        $templateData = MessageTemplateService::getOrderNotificationData($order);

        $data = [
            'type' => 'order_notification',
            'order_id' => $order->id,
            'notification_type' => $type,
        ];

        // Get formatted notification for push channel
        $pushNotification = MessageTemplateService::formatNotification($type, 'push', $templateData);
        $title = $pushNotification['title'];
        $message = $pushNotification['body'];

        return $this->sendToUser($recipient, $title, $message, $data, $channels);
    }

    /**
     * Send bulk notifications to multiple users
     *
     * @param array $users
     * @param string $title
     * @param string $message
     * @param array $data
     * @param array $channels
     * @return array
     */
    public function sendBulkNotification(array $users, string $title, string $message, array $data = [], array $channels = ['push']): array
    {
        $results = [];

        foreach ($users as $user) {
            $results[$user->id] = $this->sendToUser($user, $title, $message, $data, $channels);
        }

        return $results;
    }

    /**
     * Send emergency blood request notification
     *
     * @param string $bloodType
     * @param string $location
     * @param string $contactInfo
     * @param array $channels
     * @return array
     */
    public function sendEmergencyBloodRequest(string $bloodType, string $location, string $contactInfo, array $channels = ['push', 'sms']): array
    {
        // Get all donors with matching blood type
        $donors = User::where('is_donor', true)
                     ->where('status', true)
                     ->whereHas('blood', function($query) use ($bloodType) {
                         $query->where('name', $bloodType);
                     })
                     ->whereNotNull('fcm')
                     ->get();

        $title = "طلب دم عاجل - {$bloodType}";
        $message = "مطلوب متبرع بفصيلة دم {$bloodType} في {$location}. للتواصل: {$contactInfo}";

        $data = [
            'type' => 'emergency_blood_request',
            'blood_type' => $bloodType,
            'location' => $location,
            'contact_info' => $contactInfo,
        ];

        return $this->sendBulkNotification($donors->toArray(), $title, $message, $data, $channels);
    }



    /**
     * Format SMS message
     *
     * @param string $title
     * @param string $message
     * @return string
     */
    protected function formatSMSMessage(string $title, string $message): string
    {
        return "{$title}\n\n{$message}\n\nبنك الدم";
    }

    /**
     * Get default push notification options
     *
     * @return array
     */
    protected function getDefaultPushOptions(): array
    {
        return [
            'android' => [
                'priority' => 'high',
                'notification' => [
                    'sound' => 'default',
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                    'channel_id' => 'blood_bank_notifications'
                ]
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                        'badge' => 1,
                        'alert' => [
                            'title' => '',
                            'body' => ''
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Subscribe user to blood type topic
     *
     * @param User $user
     * @return bool
     */
    public function subscribeToBloodTypeTopic(User $user): bool
    {
        if (!$user->fcm || !$user->blood) {
            return false;
        }

        $topic = 'blood_type_' . strtolower($user->blood->name);
        return $this->firebaseService->subscribeToTopic($user->fcm, $topic);
    }

    /**
     * Unsubscribe user from blood type topic
     *
     * @param User $user
     * @return bool
     */
    public function unsubscribeFromBloodTypeTopic(User $user): bool
    {
        if (!$user->fcm || !$user->blood) {
            return false;
        }

        $topic = 'blood_type_' . strtolower($user->blood->name);
        return $this->firebaseService->unsubscribeFromTopic($user->fcm, $topic);
    }
}
