<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Disease;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DiseaseController extends Controller
{
    /**
     * Display a listing of diseases
     */
    public function index()
    {
        $diseases = Disease::select('id','name','more_info')->where('status', true)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $diseases
        ]);
    }

    /**
     * Store a newly created disease (Admin only)
     */
    public function store(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:disease',
            'more_info' => 'sometimes|string',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $disease = Disease::create([
            'name' => $request->name,
            'more_info' => $request->more_info,
            'user_id' => $request->user()->id,
            'status' => $request->status ?? true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Disease created successfully',
            'data' => $disease->load('creator')
        ], 201);
    }

    /**
     * Display the specified disease
     */
    public function show(Disease $disease)
    {
        return response()->json([
            'success' => true,
            'data' => $disease->load(['creator', 'users' => function($query) {
                $query->where('status', true);
            }])
        ]);
    }

    /**
     * Update the specified disease (Admin only)
     */
    public function update(Request $request, Disease $disease)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:disease,name,' . $disease->id,
            'more_info' => 'sometimes|string',
            'status' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $disease->update($request->only(['name', 'more_info', 'status']));

        return response()->json([
            'success' => true,
            'message' => 'Disease updated successfully',
            'data' => $disease->fresh()->load('creator')
        ]);
    }

    /**
     * Remove the specified disease (Admin only)
     */
    public function destroy(Request $request, Disease $disease)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Check if disease is associated with users
        if ($disease->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete disease. It is associated with users.'
            ], 400);
        }

        $disease->delete();

        return response()->json([
            'success' => true,
            'message' => 'Disease deleted successfully'
        ]);
    }

    /**
     * Associate disease with authenticated user
     */
    public function associate(Request $request, Disease $disease)
    {
        $user = $request->user();

        // Check if already associated
        if ($user->diseases()->where('disease_id', $disease->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Disease already associated with user'
            ], 400);
        }

        $user->diseases()->attach($disease->id);

        return response()->json([
            'success' => true,
            'message' => 'Disease associated successfully'
        ]);
    }

    /**
     * Disassociate disease from authenticated user
     */
    public function disassociate(Request $request, Disease $disease)
    {
        $user = $request->user();

        // Check if associated
        if (!$user->diseases()->where('disease_id', $disease->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Disease not associated with user'
            ], 400);
        }

        $user->diseases()->detach($disease->id);

        return response()->json([
            'success' => true,
            'message' => 'Disease disassociated successfully'
        ]);
    }

    /**
     * Get user's diseases
     */
    public function userDiseases(Request $request)
    {
        $diseases = $request->user()->diseases()->where('status', true)->get();

        return response()->json([
            'success' => true,
            'data' => $diseases
        ]);
    }
}
