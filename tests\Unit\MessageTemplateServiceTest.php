<?php

namespace Tests\Unit;

use App\Services\MessageTemplateService;
use App\Models\User;
use App\Models\Order;
use App\Models\Blood;
use App\Models\HealthCenter;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MessageTemplateServiceTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function can_get_notification_templates()
    {
        $templates = MessageTemplateService::getNotificationTemplates();

        $this->assertIsArray($templates);
        $this->assertArrayHasKey('new_order', $templates);
        $this->assertArrayHasKey('order_accepted', $templates);
        $this->assertArrayHasKey('order_rejected', $templates);
        $this->assertArrayHasKey('order_completed', $templates);
    }

    /** @test */
    public function can_get_sms_templates()
    {
        $templates = MessageTemplateService::getSMSTemplates();

        $this->assertIsArray($templates);
        $this->assertArray<PERSON><PERSON><PERSON>ey('verification_code', $templates);
        $this->assertArray<PERSON><PERSON><PERSON>ey('password_reset', $templates);
    }

    /** @test */
    public function can_replace_placeholders()
    {
        $template = 'مرحباً {user_name}، لديك {count} رسائل جديدة';
        $data = [
            'user_name' => 'أحمد',
            'count' => '5'
        ];

        $result = MessageTemplateService::replacePlaceholders($template, $data);

        $this->assertEquals('مرحباً أحمد، لديك 5 رسائل جديدة', $result);
    }

    /** @test */
    public function can_get_order_notification_data()
    {
        $blood = Blood::factory()->create(['name' => 'A+']);
        $healthCenter = HealthCenter::factory()->create(['name' => 'مستشفى الملك فهد']);
        $fromUser = User::factory()->create(['name' => 'أحمد محمد']);
        $toUser = User::factory()->create(['name' => 'سارة أحمد']);

        $order = Order::factory()->create([
            'from_user_id' => $fromUser->id,
            'to_user_id' => $toUser->id,
            'blood_id' => $blood->id,
            'health_center_id' => $healthCenter->id,
            'message' => 'أحتاج للتبرع بالدم',
            'status' => 'pending'
        ]);

        $data = MessageTemplateService::getOrderNotificationData($order);

        $this->assertArrayHasKey('from_user_name', $data);
        $this->assertArrayHasKey('to_user_name', $data);
        $this->assertArrayHasKey('blood_type', $data);
        $this->assertArrayHasKey('health_center', $data);
        $this->assertEquals('أحمد محمد', $data['from_user_name']);
        $this->assertEquals('سارة أحمد', $data['to_user_name']);
        $this->assertEquals('A+', $data['blood_type']);
        $this->assertEquals('مستشفى الملك فهد', $data['health_center']);
    }

    /** @test */
    public function can_get_user_notification_data()
    {
        $blood = Blood::factory()->create(['name' => 'O-']);
        $user = User::factory()->create([
            'name' => 'محمد علي',
            'phone' => '+966501234567',
            'email' => '<EMAIL>',
            'is_donor' => true,
            'blood_id' => $blood->id
        ]);

        $data = MessageTemplateService::getUserNotificationData($user);

        $this->assertArrayHasKey('user_name', $data);
        $this->assertArrayHasKey('blood_type', $data);
        $this->assertArrayHasKey('is_donor', $data);
        $this->assertEquals('محمد علي', $data['user_name']);
        $this->assertEquals('O-', $data['blood_type']);
        $this->assertEquals('نعم', $data['is_donor']);
    }

    /** @test */
    public function can_format_notification_for_push_channel()
    {
        $data = [
            'from_user_name' => 'أحمد',
            'to_user_name' => 'سارة',
            'blood_type' => 'A+',
            'health_center' => 'مستشفى الملك فهد'
        ];

        $result = MessageTemplateService::formatNotification('new_order', 'push', $data);

        $this->assertArrayHasKey('title', $result);
        $this->assertArrayHasKey('body', $result);
        $this->assertStringContainsString('طلب تبرع جديد', $result['title']);
        $this->assertStringContainsString('أحمد', $result['body']);
        $this->assertStringContainsString('A+', $result['body']);
    }

    /** @test */
    public function can_format_notification_for_sms_channel()
    {
        $data = [
            'from_user_name' => 'أحمد',
            'blood_type' => 'A+',
            'health_center' => 'مستشفى الملك فهد'
        ];

        $result = MessageTemplateService::formatNotification('new_order', 'sms', $data);

        $this->assertArrayHasKey('message', $result);
        $this->assertStringContainsString('أحمد', $result['message']);
        $this->assertStringContainsString('A+', $result['message']);
    }

    /** @test */
    public function throws_exception_for_unknown_template()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Template 'unknown_template' not found");

        MessageTemplateService::formatNotification('unknown_template', 'push', []);
    }

    /** @test */
    public function throws_exception_for_unsupported_channel()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Unsupported channel: unknown_channel");

        MessageTemplateService::formatNotification('new_order', 'unknown_channel', []);
    }

    /** @test */
    public function can_validate_template_data()
    {
        $template = 'مرحباً {user_name}، لديك طلب من {from_user} في {location}';
        $data = [
            'user_name' => 'أحمد',
            'from_user' => 'سارة'
            // missing 'location'
        ];

        $missing = MessageTemplateService::validateTemplateData($template, $data);

        $this->assertContains('location', $missing);
        $this->assertNotContains('user_name', $missing);
        $this->assertNotContains('from_user', $missing);
    }

    /** @test */
    public function returns_empty_array_when_all_placeholders_are_provided()
    {
        $template = 'مرحباً {user_name}، لديك {count} رسائل';
        $data = [
            'user_name' => 'أحمد',
            'count' => '5'
        ];

        $missing = MessageTemplateService::validateTemplateData($template, $data);

        $this->assertEmpty($missing);
    }

    /** @test */
    public function can_get_template_by_key_and_type()
    {
        $template = MessageTemplateService::getTemplate('new_order', 'notification');
        
        $this->assertIsArray($template);
        $this->assertArrayHasKey('title', $template);
        $this->assertArrayHasKey('message', $template);

        $smsTemplate = MessageTemplateService::getTemplate('verification_code', 'sms');
        
        $this->assertIsString($smsTemplate);
        $this->assertStringContainsString('{code}', $smsTemplate);
    }

    /** @test */
    public function returns_null_for_unknown_template_key()
    {
        $template = MessageTemplateService::getTemplate('unknown_key', 'notification');
        
        $this->assertNull($template);
    }

    /** @test */
    public function returns_null_for_unknown_template_type()
    {
        $template = MessageTemplateService::getTemplate('new_order', 'unknown_type');
        
        $this->assertNull($template);
    }
}
