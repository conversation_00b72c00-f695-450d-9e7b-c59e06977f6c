# 🩸 Blood Bank API - دليل المطور

## 📋 نظرة عامة

مرحباً بك في دوكمنتيشن مشروع بنك الدم! هذا المشروع عبارة عن منصة شاملة لإدارة التبرع بالدم تربط بين المتبرعين والمحتاجين للدم عبر تطبيق موبايل.

## 🚀 البدء السريع

### 1. معلومات الاتصال
- **Base URL**: `http://your-domain.com/api`
- **Authentication**: Bearer <PERSON> (Laravel Sanctum)
- **Content-Type**: `application/json`

### 2. الحصول على Token
```bash
# التسجيل
curl -X POST http://your-domain.com/api/register \
  -H "Content-Type: application/json" \
  -d '{"name":"أحمد محمد","email":"<EMAIL>","phone":"+************","password":"password123","password_confirmation":"password123","gender":true,"birthdate":"1990-01-01","blood_id":1}'

# تسجيل الدخول
curl -X POST http://your-domain.com/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 3. استخدام Token
```bash
curl -X GET http://your-domain.com/api/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 📚 الملفات المهمة

### 📖 الدوكمنتيشن الشامل
- **[COMPLETE_API_DOCUMENTATION.md](./COMPLETE_API_DOCUMENTATION.md)** - دوكمنتيشن شامل لجميع APIs
- **[ACCOUNT_VERIFICATION_GUIDE.md](./ACCOUNT_VERIFICATION_GUIDE.md)** - دليل نظام التحقق من الحساب
- **[FIREBASE_SETUP.md](./FIREBASE_SETUP.md)** - دليل إعداد Firebase
- **[FIREBASE_SMS_GUIDE.md](./FIREBASE_SMS_GUIDE.md)** - دليل Firebase SMS

### 🧪 اختبار APIs
- **[Blood_Bank_API_Complete.postman_collection.json](./Blood_Bank_API_Complete.postman_collection.json)** - مجموعة Postman شاملة
- **[security_report.html](./security_report.html)** - تقرير الأمان

## 🔐 المصادقة والأمان

### نظام المصادقة
- **Laravel Sanctum** لإدارة API tokens
- **Bearer Token Authentication**
- **Admin Middleware** للصلاحيات المتقدمة

### Headers المطلوبة
```http
Authorization: Bearer {your_api_token}
Content-Type: application/json
Accept: application/json
```

## 📱 الميزات الرئيسية

### 🔐 Authentication
- تسجيل المستخدمين مع التحقق عبر SMS
- تسجيل الدخول والخروج
- إدارة الملف الشخصي

### 🩸 إدارة أنواع الدم
- عرض أنواع الدم المتاحة
- البحث عن المتبرعين حسب فصيلة الدم
- إدارة أنواع الدم (Admin)

### 📋 نظام الطلبات
- إنشاء طلبات التبرع
- تتبع حالة الطلبات
- إدارة الطلبات المرسلة والمستلمة

### 🏥 المراكز الصحية
- قائمة المراكز الصحية
- ربط الطلبات بالمراكز
- إدارة المراكز (Admin)

### 🔔 نظام الإشعارات
- إشعارات Push عبر Firebase
- رسائل SMS
- إشعارات الطوارئ
- تخصيص تفضيلات الإشعارات

### 📊 التقارير
- نظام التقارير للمستخدمين
- إدارة أسباب التقارير
- مراجعة التقارير (Admin)

## 🛠️ أدوات التطوير

### Postman Collection
استورد ملف `Blood_Bank_API_Complete.postman_collection.json` في Postman:

1. افتح Postman
2. اضغط Import
3. اختر الملف
4. اضبط متغير `base_url`
5. ابدأ الاختبار!

### متغيرات Postman
```json
{
  "base_url": "http://your-domain.com/api",
  "api_token": "your_user_token",
  "admin_token": "your_admin_token"
}
```

## 📊 أكواد الاستجابة

| Code | المعنى | الوصف |
|------|--------|--------|
| 200 | Success | العملية نجحت |
| 201 | Created | تم إنشاء المورد بنجاح |
| 400 | Bad Request | خطأ في البيانات المرسلة |
| 401 | Unauthorized | غير مصرح بالوصول |
| 403 | Forbidden | ممنوع - تحتاج صلاحيات أعلى |
| 404 | Not Found | المورد غير موجود |
| 422 | Validation Error | خطأ في التحقق من البيانات |
| 500 | Server Error | خطأ في الخادم |

## 🔄 تدفق العمل النموذجي

### للمستخدم العادي:
1. **التسجيل** → `POST /api/register`
2. **التحقق من الحساب** → `POST /api/verify-account`
3. **تسجيل الدخول** → `POST /api/login`
4. **البحث عن متبرعين** → `GET /api/blood/{id}/donors`
5. **إنشاء طلب** → `POST /api/orders`
6. **متابعة الطلبات** → `GET /api/orders`

### للمدير:
1. **تسجيل الدخول** → `POST /api/login`
2. **إدارة أنواع الدم** → `POST /api/blood`
3. **إرسال إشعارات عاجلة** → `POST /api/notifications/urgent-blood-request`
4. **مراجعة التقارير** → `GET /api/reports`

## 🌐 أمثلة بلغات البرمجة

### JavaScript (Fetch)
```javascript
// تسجيل الدخول
const login = async (email, password) => {
  const response = await fetch('/api/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, password })
  });
  
  const data = await response.json();
  if (data.success) {
    localStorage.setItem('token', data.data.token);
  }
  return data;
};

// إنشاء طلب
const createOrder = async (orderData) => {
  const token = localStorage.getItem('token');
  const response = await fetch('/api/orders', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(orderData)
  });
  
  return await response.json();
};
```

### Flutter/Dart
```dart
class ApiService {
  static const String baseUrl = 'http://your-domain.com/api';
  
  static Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/login'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'email': email, 'password': password}),
    );
    
    return json.decode(response.body);
  }
}
```

### Python (Requests)
```python
import requests

class BloodBankAPI:
    def __init__(self, base_url):
        self.base_url = base_url
        self.token = None
    
    def login(self, email, password):
        response = requests.post(
            f"{self.base_url}/login",
            json={"email": email, "password": password}
        )
        
        data = response.json()
        if data.get('success'):
            self.token = data['data']['token']
        
        return data
    
    def create_order(self, order_data):
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            f"{self.base_url}/orders",
            json=order_data,
            headers=headers
        )
        
        return response.json()
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **401 Unauthorized**
   - تأكد من وجود token صحيح
   - تحقق من انتهاء صلاحية الـ token

2. **422 Validation Error**
   - راجع البيانات المرسلة
   - تأكد من تطابق validation rules

3. **403 Forbidden**
   - تحتاج صلاحيات admin
   - تأكد من استخدام admin token

4. **404 Not Found**
   - تحقق من صحة الـ endpoint
   - تأكد من وجود المورد المطلوب

## 📞 الدعم والمساعدة

- **الدوكمنتيشن الشامل**: [COMPLETE_API_DOCUMENTATION.md](./COMPLETE_API_DOCUMENTATION.md)
- **تقرير الأمان**: [security_report.html](./security_report.html)
- **Postman Collection**: [Blood_Bank_API_Complete.postman_collection.json](./Blood_Bank_API_Complete.postman_collection.json)

---

## 🎯 الخطوات التالية

1. **اقرأ الدوكمنتيشن الشامل** للحصول على تفاصيل كاملة
2. **استورد Postman Collection** لاختبار APIs
3. **ابدأ التطوير** باستخدام الأمثلة المقدمة
4. **راجع تقرير الأمان** لفهم آليات الحماية

**مشروع بنك الدم جاهز للاستخدام! 🚀**
