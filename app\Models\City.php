<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class City extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'status',
        'state_id',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the state that owns this city
     */
    public function state()
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get all health centers in this city
     */
    public function healthCenters()
    {
        return $this->hasMany(HealthCenter::class);
    }
}
