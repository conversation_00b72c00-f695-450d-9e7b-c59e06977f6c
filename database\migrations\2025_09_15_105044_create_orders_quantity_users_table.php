<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders_quantity_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('orders_quantity_id')->index();
            $table->foreign('orders_quantity_id')->references('id')->on('orders_quantity')->onDelete('restrict');
            $table->unsignedBigInteger('donor_id')->index();
            $table->foreign('donor_id')->references('id')->on('users')->onDelete('restrict');
            $table->integer('quantity');
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders_quantity_users');
    }
};
