<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'message',
        'date',
        'status',
        'from_user_id',
        'to_user_id',
        'blood_id',
        'health_center_id',
    ];

    protected $casts = [
        'date' => 'datetime',
    ];

    /**
     * Get the user who made the order (requester)
     */
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * Get the user who received the order (donor)
     */
    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    /**
     * Get the blood type for this order
     */
    public function blood()
    {
        return $this->belongsTo(Blood::class);
    }

    /**
     * Get the health center for this order
     */
    public function healthCenter()
    {
        return $this->belongsTo(HealthCenter::class);
    }
}
