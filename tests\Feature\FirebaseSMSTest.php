<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\SMSService;
use App\Services\FirebaseSMSService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class FirebaseSMSTest extends TestCase
{
    use RefreshDatabase;

    protected $smsService;
    protected $firebaseSMSService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock Firebase SMS Service
        $this->firebaseSMSService = Mockery::mock(FirebaseSMSService::class);
        $this->app->instance(FirebaseSMSService::class, $this->firebaseSMSService);

        // Set Firebase as SMS provider
        config(['services.sms.provider' => 'firebase']);
    }

    /** @test */
    public function can_get_firebase_sms_service_status()
    {
        $response = $this->getJson('/api/firebase-sms/status');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'current_sms_provider' => 'firebase'
                ]
            ]);
    }

    /** @test */
    public function can_format_phone_number()
    {
        $response = $this->postJson('/api/firebase-sms/format-phone', [
            'phone_number' => '**********',
            'country_code' => '+966'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'original' => '**********',
                    'formatted' => '+966501234567',
                    'country_code' => '+966'
                ]
            ]);
    }

    /** @test */
    public function can_send_verification_code()
    {
        $this->firebaseSMSService
            ->shouldReceive('formatPhoneNumber')
            ->once()
            ->with('+966501234567')
            ->andReturn('+966501234567');

        $this->firebaseSMSService
            ->shouldReceive('sendVerificationCode')
            ->once()
            ->with('+966501234567', null)
            ->andReturn([
                'success' => true,
                'session_info' => 'test-session-info',
                'message' => 'Verification code sent successfully'
            ]);

        // Mock SMS service to return Firebase as available
        $smsService = Mockery::mock(SMSService::class);
        $smsService->shouldReceive('isFirebaseAvailable')->andReturn(true);
        $smsService->shouldReceive('sendFirebaseVerificationCode')
            ->with('+966501234567', null)
            ->andReturn([
                'success' => true,
                'session_info' => 'test-session-info',
                'message' => 'Verification code sent successfully'
            ]);

        $this->app->instance(SMSService::class, $smsService);

        $response = $this->postJson('/api/firebase-sms/send-verification', [
            'phone_number' => '+966501234567'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Verification code sent successfully',
                'session_info' => 'test-session-info'
            ]);
    }

    /** @test */
    public function can_verify_phone_number()
    {
        $this->firebaseSMSService
            ->shouldReceive('verifyPhoneNumber')
            ->once()
            ->with('test-session-info', '123456')
            ->andReturn([
                'success' => true,
                'id_token' => 'test-id-token',
                'phone_number' => '+966501234567',
                'message' => 'Phone number verified successfully'
            ]);

        // Mock SMS service
        $smsService = Mockery::mock(SMSService::class);
        $smsService->shouldReceive('isFirebaseAvailable')->andReturn(true);
        $smsService->shouldReceive('verifyFirebasePhoneNumber')
            ->with('test-session-info', '123456')
            ->andReturn([
                'success' => true,
                'id_token' => 'test-id-token',
                'phone_number' => '+966501234567',
                'message' => 'Phone number verified successfully'
            ]);

        $this->app->instance(SMSService::class, $smsService);

        $response = $this->postJson('/api/firebase-sms/verify-phone', [
            'session_info' => 'test-session-info',
            'code' => '123456'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Phone number verified successfully',
                'id_token' => 'test-id-token',
                'phone_number' => '+966501234567'
            ]);
    }

    /** @test */
    public function admin_can_send_custom_sms()
    {
        $admin = User::factory()->create(['is_admin' => true]);

        // Mock SMS service
        $smsService = Mockery::mock(SMSService::class);
        $smsService->shouldReceive('isFirebaseAvailable')->andReturn(true);
        $smsService->shouldReceive('send')
            ->with('+966501234567', 'Test custom message', Mockery::any())
            ->andReturn(true);

        $this->app->instance(SMSService::class, $smsService);

        $response = $this->actingAs($admin, 'sanctum')
            ->postJson('/api/firebase-sms/send-custom', [
                'phone_number' => '+966501234567',
                'message' => 'Test custom message',
                'data' => ['key' => 'value']
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Custom SMS sent successfully via Firebase'
            ]);
    }

    /** @test */
    public function non_admin_cannot_send_custom_sms()
    {
        $user = User::factory()->create(['is_admin' => false]);

        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/firebase-sms/send-custom', [
                'phone_number' => '+966501234567',
                'message' => 'Test custom message'
            ]);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ]);
    }

    /** @test */
    public function can_get_message_info()
    {
        $user = User::factory()->create();

        // Mock SMS service
        $smsService = Mockery::mock(SMSService::class);
        $smsService->shouldReceive('getMessageInfo')
            ->with('Test message')
            ->andReturn([
                'length' => 12,
                'parts' => 1,
                'is_unicode' => false,
                'remaining_chars' => 148
            ]);

        $this->app->instance(SMSService::class, $smsService);

        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/firebase-sms/message-info', [
                'message' => 'Test message'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'length' => 12,
                    'parts' => 1,
                    'is_unicode' => false,
                    'remaining_chars' => 148
                ]
            ]);
    }

    /** @test */
    public function validation_fails_for_invalid_phone_number()
    {
        $response = $this->postJson('/api/firebase-sms/send-verification', [
            'phone_number' => ''
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation errors'
            ])
            ->assertJsonValidationErrors(['phone_number']);
    }

    /** @test */
    public function validation_fails_for_invalid_verification_code()
    {
        $response = $this->postJson('/api/firebase-sms/verify-phone', [
            'session_info' => 'test-session',
            'code' => '12' // Too short
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation errors'
            ])
            ->assertJsonValidationErrors(['code']);
    }

    /** @test */
    public function returns_error_when_firebase_not_available()
    {
        // Mock SMS service to return Firebase as not available
        $smsService = Mockery::mock(SMSService::class);
        $smsService->shouldReceive('isFirebaseAvailable')->andReturn(false);

        $this->app->instance(SMSService::class, $smsService);

        $response = $this->postJson('/api/firebase-sms/send-verification', [
            'phone_number' => '+966501234567'
        ]);

        $response->assertStatus(503)
            ->assertJson([
                'success' => false,
                'message' => 'Firebase SMS service is not available or not configured'
            ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
