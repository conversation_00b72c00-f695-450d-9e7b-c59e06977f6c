<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders_quantity', function (Blueprint $table) {
            $table->id();
            $table->integer('quantity');
            $table->boolean('status')->default(true);
            $table->date('date');
            $table->text('more_info')->nullable();
            $table->unsignedBigInteger('user_id')->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('restrict');
            $table->unsignedBigInteger('blood_id')->index();
            $table->foreign('blood_id')->references('id')->on('blood')->onDelete('restrict');
            $table->unsignedBigInteger('health_center_id')->index();
            $table->foreign('health_center_id')->references('id')->on('health_center')->onDelete('restrict');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders_quantity');
    }
};
