<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Blood;
use App\Models\State;
use App\Models\City;
use App\Models\Disease;
use App\Models\ReportsReason;
use App\Models\User;
use App\Models\HealthCenter;
use Illuminate\Support\Facades\Hash;

class BloodBankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Blood Types
        $bloodTypes = [
            'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
        ];

        foreach ($bloodTypes as $type) {
            Blood::create([
                'name' => $type,
                'status' => true,
            ]);
        }

        // Create States
        $states = [
            'صنعاء', 'إب', 'ذمار', 'تعز', 'الحديدة', 'مأرب', 'صعدة', 'المحويت'
        ];

        foreach ($states as $stateName) {
            State::create([
                'name' => $stateName,
                'status' => true,
            ]);
        }

        // Create Cities
        $cities = [
            ['name' => 'همدان', 'state_id' => 1],
            ['name' => 'أرحب', 'state_id' => 1],
            ['name' => 'بني حشيش', 'state_id' => 1],
            ['name' => 'السدة', 'state_id' => 2],
            ['name' => 'السياني', 'state_id' => 2],
            ['name' => 'الشعر', 'state_id' => 2],
            ['name' => 'التعزية', 'state_id' => 3],
            ['name' => 'حيفان', 'state_id' => 3],
            ['name' => 'شرعب', 'state_id' => 3],
        ];

        foreach ($cities as $city) {
            City::create($city + ['status' => true]);
        }

        // Create Diseases
        $diseases = [
            'السكري', 'ضغط الدم', 'أمراض القلب', 'الربو', 'التهاب الكبد', 'فقر الدم'
        ];

        // Create Admin User first
        $admin = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'phone' => '07700000000',
            'password' => Hash::make('password123'),
            'first_contact_phone' => '07700000001',
            'second_contact_phone' => '07700000002',
            'is_admin' => true,
            'status' => true,
            'is_donor' => false,
            'gender' => true,
            'birthdate' => '1990-01-01',
            'blood_id' => 1,
        ]);

        foreach ($diseases as $diseaseName) {
            Disease::create([
                'name' => $diseaseName,
                'status' => true,
                'user_id' => $admin->id,
            ]);
        }

        // Create Report Reasons
        $reportReasons = [
            'سلوك غير لائق',
            'معلومات خاطئة',
            'عدم الالتزام بالمواعيد',
            'طلبات وهمية',
            'محتوى مسيء'
        ];

        foreach ($reportReasons as $reason) {
            ReportsReason::create([
                'reason' => $reason,
                'status' => true,
            ]);
        }

        // Create Sample Users
        $users = [
            [
                'name' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'phone' => '07701111111',
                'first_contact_phone' => '07701111112',
                'second_contact_phone' => '07701111113',
                'is_donor' => true,
                'blood_id' => 1, // A+
            ],
            [
                'name' => 'فاطمة علي',
                'email' => '<EMAIL>',
                'phone' => '07702222222',
                'first_contact_phone' => '07702222223',
                'second_contact_phone' => '07702222224',
                'is_donor' => true,
                'blood_id' => 2, // A-
            ],
            [
                'name' => 'محمد حسن',
                'email' => '<EMAIL>',
                'phone' => '07703333333',
                'first_contact_phone' => '07703333334',
                'second_contact_phone' => '07703333335',
                'is_donor' => false,
                'blood_id' => 3, // B+
            ],
        ];

        foreach ($users as $userData) {
            User::create($userData + [
                'password' => Hash::make('password123'),
                'is_admin' => false,
                'status' => true,
                'gender' => true,
                'birthdate' => '1990-01-01',
            ]);
        }

        // Create Health Centers
        $healthCenters = [
            [
                'name' => 'مستشفى بغداد التعليمي',
                'address' => 'الكرخ - بغداد',
                'phone' => '07800000001',
                'email' => '<EMAIL>',
                'city_id' => 1,
                'user_id' => $admin->id,
            ],
            [
                'name' => 'مركز البصرة الصحي',
                'address' => 'الزبير - البصرة',
                'phone' => '***********',
                'email' => '<EMAIL>',
                'city_id' => 3,
                'user_id' => $admin->id,
            ],
        ];

        foreach ($healthCenters as $center) {
            HealthCenter::create($center + ['status' => true]);
        }

        $this->command->info('Blood Bank sample data seeded successfully!');
    }
}
