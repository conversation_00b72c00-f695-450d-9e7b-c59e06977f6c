<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class SendNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;
    protected $title;
    protected $message;
    protected $data;
    protected $channels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * Create a new job instance.
     *
     * @param int $userId
     * @param string $title
     * @param string $message
     * @param array $data
     * @param array $channels
     */
    public function __construct(int $userId, string $title, string $message, array $data = [], array $channels = ['push', 'sms'])
    {
        $this->userId = $userId;
        $this->title = $title;
        $this->message = $message;
        $this->data = $data;
        $this->channels = $channels;
    }

    /**
     * Execute the job.
     *
     * @param NotificationService $notificationService
     * @return void
     */
    public function handle(NotificationService $notificationService): void
    {
        try {
            $user = User::find($this->userId);
            
            if (!$user) {
                Log::warning('User not found for notification job', ['user_id' => $this->userId]);
                return;
            }

            $result = $notificationService->sendToUser(
                $user,
                $this->title,
                $this->message,
                $this->data,
                $this->channels
            );

            Log::info('Notification job completed successfully', [
                'user_id' => $this->userId,
                'title' => $this->title,
                'channels' => $this->channels,
                'result' => $result
            ]);

        } catch (Exception $e) {
            Log::error('Notification job failed: ' . $e->getMessage(), [
                'user_id' => $this->userId,
                'title' => $this->title,
                'error' => $e->getMessage()
            ]);

            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param Exception $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('Notification job failed permanently', [
            'user_id' => $this->userId,
            'title' => $this->title,
            'channels' => $this->channels,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);
    }
}
