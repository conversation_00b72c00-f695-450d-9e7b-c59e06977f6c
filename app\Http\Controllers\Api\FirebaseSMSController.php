<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SMSService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FirebaseSMSController extends Controller
{
    protected $smsService;

    public function __construct(SMSService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Send verification code via Firebase SMS
     */
    public function sendVerificationCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string',
            'recaptcha_token' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if Firebase SMS is available
        if (!$this->smsService->isFirebaseAvailable()) {
            return response()->json([
                'success' => false,
                'message' => 'Firebase SMS service is not available or not configured'
            ], 503);
        }

        $result = $this->smsService->sendFirebaseVerificationCode(
            $request->phone_number,
            $request->recaptcha_token
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'session_info' => $result['session_info']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $result['message'],
            'error' => $result['error'] ?? 'Unknown error'
        ], 400);
    }

    /**
     * Verify phone number with code
     */
    public function verifyPhoneNumber(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_info' => 'required|string',
            'code' => 'required|string|min:4|max:8'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if Firebase SMS is available
        if (!$this->smsService->isFirebaseAvailable()) {
            return response()->json([
                'success' => false,
                'message' => 'Firebase SMS service is not available or not configured'
            ], 503);
        }

        $result = $this->smsService->verifyFirebasePhoneNumber(
            $request->session_info,
            $request->code
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'id_token' => $result['id_token'],
                'phone_number' => $result['phone_number']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $result['message'],
            'error' => $result['error'] ?? 'Verification failed'
        ], 400);
    }

    /**
     * Check Firebase SMS service status
     */
    public function getServiceStatus()
    {
        $isAvailable = $this->smsService->isFirebaseAvailable();
        $provider = config('services.sms.provider');

        return response()->json([
            'success' => true,
            'data' => [
                'firebase_sms_available' => $isAvailable,
                'current_sms_provider' => $provider,
                'firebase_configured' => $provider === 'firebase' && $isAvailable
            ]
        ]);
    }

    /**
     * Send custom SMS via Firebase (Admin only)
     */
    public function sendCustomSMS(Request $request)
    {
        // Check if user is admin
        if (!$request->user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string',
            'message' => 'required|string|max:1000',
            'data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if Firebase SMS is available
        if (!$this->smsService->isFirebaseAvailable()) {
            return response()->json([
                'success' => false,
                'message' => 'Firebase SMS service is not available or not configured'
            ], 503);
        }

        // Use the regular send method with Firebase provider
        $success = $this->smsService->send(
            $request->phone_number,
            $request->message,
            array_merge($request->get('data', []), ['type' => 'custom'])
        );

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Custom SMS sent successfully via Firebase'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to send custom SMS via Firebase'
        ], 500);
    }

    /**
     * Get SMS message info (character count, parts, etc.)
     */
    public function getMessageInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $messageInfo = $this->smsService->getMessageInfo($request->message);

        return response()->json([
            'success' => true,
            'data' => $messageInfo
        ]);
    }

    /**
     * Format phone number for Firebase
     */
    public function formatPhoneNumber(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string',
            'country_code' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $countryCode = $request->get('country_code', '+966');
        $formattedNumber = $this->smsService->formatPhoneNumber($request->phone_number, $countryCode);
        $isValid = $this->smsService->validatePhoneNumber($formattedNumber);

        return response()->json([
            'success' => true,
            'data' => [
                'original' => $request->phone_number,
                'formatted' => $formattedNumber,
                'is_valid' => $isValid,
                'country_code' => $countryCode
            ]
        ]);
    }
}
