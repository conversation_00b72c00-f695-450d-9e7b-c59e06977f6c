# Blood Bank System - Models Documentation

## تم إنشاء جميع الـ Models التالية مع العلاقات المطلوبة:

### 1. Blood Model (`app/Models/Blood.php`)
- **الجدول**: `blood`
- **الحقول**: `id`, `name`, `status`, `deleted_at`, `created_at`, `updated_at`
- **العلاقات**:
  - `users()` - hasMany: جميع المستخدمين بهذا النوع من الدم
  - `orders()` - hasMany: جميع الطلبات لهذا النوع من الدم
  - `ordersQuantity()` - hasMany: جميع كميات الطلبات لهذا النوع من الدم

### 2. State Model (`app/Models/State.php`)
- **الجدول**: `states`
- **الحقول**: `id`, `name`, `status`, `created_at`, `updated_at`
- **العلاقات**:
  - `cities()` - hasMany: جميع المدن في هذه المحافظة
  - `healthCenters()` - hasManyThrough: جميع المراكز الصحية في هذه المحافظة

### 3. City Model (`app/Models/City.php`)
- **الجدول**: `cities`
- **الحقول**: `id`, `name`, `status`, `state_id`, `created_at`, `updated_at`
- **العلاقات**:
  - `state()` - belongsTo: المحافظة التي تنتمي إليها هذه المدينة
  - `healthCenters()` - hasMany: جميع المراكز الصحية في هذه المدينة

### 4. Disease Model (`app/Models/Disease.php`)
- **الجدول**: `disease`
- **الحقول**: `id`, `name`, `status`, `more_info`, `user_id`, `created_at`, `updated_at`
- **العلاقات**:
  - `creator()` - belongsTo: المستخدم الذي أنشأ هذا المرض
  - `users()` - belongsToMany: جميع المستخدمين المصابين بهذا المرض
  - `diseaseUsers()` - hasMany: جميع سجلات الجدول الوسطي

### 5. ReportsReason Model (`app/Models/ReportsReason.php`)
- **الجدول**: `reports_reasons`
- **الحقول**: `id`, `reason`, `status`, `created_at`, `updated_at`
- **العلاقات**:
  - `reports()` - hasMany: جميع التقارير التي تستخدم هذا السبب

### 6. HealthCenter Model (`app/Models/HealthCenter.php`)
- **الجدول**: `health_center`
- **الحقول**: `id`, `name`, `address`, `phone`, `email`, `image`, `city_id`, `status`, `more_info`, `user_id`, `created_at`, `updated_at`
- **العلاقات**:
  - `city()` - belongsTo: المدينة التي يقع فيها المركز
  - `state()` - hasOneThrough: المحافظة عبر المدينة
  - `manager()` - belongsTo: المستخدم المسؤول عن المركز
  - `users()` - belongsToMany: جميع المستخدمين المرتبطين بالمركز
  - `healthCenterUsers()` - hasMany: جميع سجلات الجدول الوسطي
  - `orders()` - hasMany: جميع الطلبات للمركز
  - `ordersQuantity()` - hasMany: جميع كميات الطلبات للمركز

### 7. Report Model (`app/Models/Report.php`)
- **الجدول**: `reports`
- **الحقول**: `id`, `more_info`, `from_user_id`, `to_user_id`, `report_reason_id`, `created_at`, `updated_at`
- **العلاقات**:
  - `fromUser()` - belongsTo: المستخدم الذي قدم التقرير
  - `toUser()` - belongsTo: المستخدم المُبلغ عنه
  - `reportReason()` - belongsTo: سبب التقرير

### 8. Order Model (`app/Models/Order.php`)
- **الجدول**: `orders`
- **الحقول**: `id`, `message`, `date`, `status`, `from_user_id`, `to_user_id`, `blood_id`, `health_center_id`, `created_at`, `updated_at`
- **العلاقات**:
  - `fromUser()` - belongsTo: المستخدم طالب الدم
  - `toUser()` - belongsTo: المستخدم المتبرع
  - `blood()` - belongsTo: نوع الدم المطلوب
  - `healthCenter()` - belongsTo: المركز الصحي

### 9. OrdersQuantity Model (`app/Models/OrdersQuantity.php`)
- **الجدول**: `orders_quantity`
- **الحقول**: `id`, `quantity`, `status`, `date`, `more_info`, `user_id`, `blood_id`, `health_center_id`, `deleted_at`, `created_at`, `updated_at`
- **العلاقات**:
  - `user()` - belongsTo: المستخدم الذي أنشأ طلب الكمية
  - `blood()` - belongsTo: نوع الدم
  - `healthCenter()` - belongsTo: المركز الصحي
  - `donors()` - belongsToMany: جميع المتبرعين لهذا الطلب
  - `ordersQuantityUsers()` - hasMany: جميع سجلات الجدول الوسطي

### 10. User Model (`app/Models/User.php`)
- **الجدول**: `users`
- **الحقول**: جميع الحقول من الـ migration
- **العلاقات الشاملة**:
  - `blood()` - belongsTo: نوع الدم
  - `diseases()` - belongsToMany: الأمراض
  - `diseaseUsers()` - hasMany: سجلات الأمراض
  - `createdDiseases()` - hasMany: الأمراض المُنشأة
  - `healthCenters()` - belongsToMany: المراكز الصحية المرتبطة
  - `healthCenterUsers()` - hasMany: سجلات المراكز الصحية
  - `managedHealthCenters()` - hasMany: المراكز الصحية المُدارة
  - `madeReports()` - hasMany: التقارير المُقدمة
  - `receivedReports()` - hasMany: التقارير المُستلمة
  - `madeOrders()` - hasMany: الطلبات المُقدمة
  - `receivedOrders()` - hasMany: الطلبات المُستلمة
  - `ordersQuantity()` - hasMany: طلبات الكمية المُنشأة
  - `donorOrdersQuantity()` - belongsToMany: طلبات الكمية كمتبرع
  - `ordersQuantityUsers()` - hasMany: سجلات طلبات الكمية

## الجداول الوسطية (Pivot Tables):

### 11. DiseaseUser Model (`app/Models/DiseaseUser.php`)
- **الجدول**: `disease_users`
- **الغرض**: ربط المستخدمين بالأمراض

### 12. HealthCenterUser Model (`app/Models/HealthCenterUser.php`)
- **الجدول**: `health_center_users`
- **الغرض**: ربط المستخدمين بالمراكز الصحية

### 13. OrdersQuantityUser Model (`app/Models/OrdersQuantityUser.php`)
- **الجدول**: `orders_quantity_users`
- **الغرض**: ربط المتبرعين بطلبات الكمية

## المشاكل التي تم إصلاحها:

1. **في ملف `reports_table.php`**: تم إضافة `nullable()` للحقول `from_user_id` و `to_user_id` لأنها تستخدم `onDelete('set null')`

## ملاحظات مهمة:

- جميع الـ Models تستخدم `SoftDeletes` حيث كان مطلوباً في الـ migration
- تم إضافة جميع العلاقات المطلوبة بناءً على الـ foreign keys في الـ migrations
- تم استخدام `withTimestamps()` في العلاقات many-to-many
- تم استخدام `withPivot()` لإضافة حقول إضافية في الجداول الوسطية
