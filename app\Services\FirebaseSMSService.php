<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Exception;

class FirebaseSMSService
{
    protected $client;
    protected $projectId;
    protected $webApiKey;
    protected $serverKey;

    public function __construct()
    {
        $this->client = new Client();
        $this->projectId = config('services.sms.firebase.project_id');
        $this->webApiKey = config('services.sms.firebase.web_api_key');
        $this->serverKey = config('services.sms.firebase.server_key');
    }

    /**
     * Send verification code via Firebase Phone Authentication
     *
     * @param string $phoneNumber
     * @param string $recaptchaToken
     * @return array
     */
    public function sendVerificationCode(string $phoneNumber, string $recaptchaToken = null): array
    {
        try {
            if (!$this->webApiKey) {
                throw new Exception('Firebase Web API Key not configured');
            }

            $url = "https://identitytoolkit.googleapis.com/v1/accounts:sendVerificationCode?key={$this->webApiKey}";

            $data = [
                'phoneNumber' => $phoneNumber,
                'recaptchaToken' => $recaptchaToken
            ];

            $response = $this->client->post($url, [
                'json' => $data,
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() === 200 && isset($result['sessionInfo'])) {
                Log::info('Firebase verification code sent successfully', [
                    'phone' => $phoneNumber,
                    'session_info' => substr($result['sessionInfo'], 0, 20) . '...'
                ]);

                return [
                    'success' => true,
                    'session_info' => $result['sessionInfo'],
                    'message' => 'Verification code sent successfully'
                ];
            }

            throw new Exception('Failed to send verification code: ' . json_encode($result));

        } catch (RequestException $e) {
            $errorBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'No response body';
            Log::error('Firebase verification code sending failed: ' . $e->getMessage(), [
                'phone' => $phoneNumber,
                'error' => $errorBody
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to send verification code'
            ];
        } catch (Exception $e) {
            Log::error('Firebase SMS service error: ' . $e->getMessage(), [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'SMS service error'
            ];
        }
    }

    /**
     * Verify phone number with code
     *
     * @param string $sessionInfo
     * @param string $code
     * @return array
     */
    public function verifyPhoneNumber(string $sessionInfo, string $code): array
    {
        try {
            if (!$this->webApiKey) {
                throw new Exception('Firebase Web API Key not configured');
            }

            $url = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPhoneNumber?key={$this->webApiKey}";

            $data = [
                'sessionInfo' => $sessionInfo,
                'code' => $code
            ];

            $response = $this->client->post($url, [
                'json' => $data,
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() === 200 && isset($result['idToken'])) {
                Log::info('Firebase phone verification successful', [
                    'phone' => $result['phoneNumber'] ?? 'unknown'
                ]);

                return [
                    'success' => true,
                    'id_token' => $result['idToken'],
                    'refresh_token' => $result['refreshToken'] ?? null,
                    'phone_number' => $result['phoneNumber'] ?? null,
                    'message' => 'Phone number verified successfully'
                ];
            }

            throw new Exception('Phone verification failed: ' . json_encode($result));

        } catch (RequestException $e) {
            $errorBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'No response body';
            Log::error('Firebase phone verification failed: ' . $e->getMessage(), [
                'session_info' => substr($sessionInfo, 0, 20) . '...',
                'error' => $errorBody
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Phone verification failed'
            ];
        } catch (Exception $e) {
            Log::error('Firebase verification service error: ' . $e->getMessage(), [
                'session_info' => substr($sessionInfo, 0, 20) . '...',
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Verification service error'
            ];
        }
    }

    /**
     * Send custom SMS via Firebase Cloud Functions
     * Note: This requires a custom Cloud Function to be deployed
     *
     * @param string $phoneNumber
     * @param string $message
     * @param array $data
     * @return array
     */
    public function sendCustomSMS(string $phoneNumber, string $message, array $data = []): array
    {
        try {
            // This would call a custom Firebase Cloud Function
            // You need to deploy a Cloud Function that handles SMS sending
            $functionUrl = config('services.sms.firebase.cloud_function_url');
            
            if (!$functionUrl) {
                // Fallback: Log the message (for development/testing)
                Log::info('Firebase custom SMS (logged only)', [
                    'phone' => $phoneNumber,
                    'message' => $message,
                    'data' => $data
                ]);

                return [
                    'success' => true,
                    'message' => 'SMS logged successfully (development mode)',
                    'note' => 'Configure FIREBASE_CLOUD_FUNCTION_URL for production SMS sending'
                ];
            }

            $response = $this->client->post($functionUrl, [
                'json' => [
                    'phoneNumber' => $phoneNumber,
                    'message' => $message,
                    'data' => $data
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $this->getAccessToken()
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() === 200) {
                Log::info('Firebase custom SMS sent successfully', [
                    'phone' => $phoneNumber,
                    'message_length' => strlen($message)
                ]);

                return [
                    'success' => true,
                    'result' => $result,
                    'message' => 'Custom SMS sent successfully'
                ];
            }

            throw new Exception('Custom SMS sending failed: ' . json_encode($result));

        } catch (Exception $e) {
            Log::error('Firebase custom SMS error: ' . $e->getMessage(), [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Custom SMS sending failed'
            ];
        }
    }

    /**
     * Get Firebase access token for authenticated requests
     *
     * @return string|null
     */
    protected function getAccessToken(): ?string
    {
        try {
            // This is a simplified version
            // In production, implement proper OAuth2 flow or use service account
            return $this->serverKey;
        } catch (Exception $e) {
            Log::error('Failed to get Firebase access token: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Format phone number for Firebase (E.164 format)
     *
     * @param string $phoneNumber
     * @param string $countryCode
     * @return string
     */
    public function formatPhoneNumber(string $phoneNumber, string $countryCode = '+966'): string
    {
        // Remove any non-digit characters
        $phoneNumber = preg_replace('/\D/', '', $phoneNumber);
        
        // If number starts with 0, remove it (Saudi Arabia format)
        if (substr($phoneNumber, 0, 1) === '0') {
            $phoneNumber = substr($phoneNumber, 1);
        }
        
        // If number doesn't start with country code, add it
        if (!str_starts_with($phoneNumber, ltrim($countryCode, '+'))) {
            $phoneNumber = ltrim($countryCode, '+') . $phoneNumber;
        }
        
        return '+' . $phoneNumber;
    }

    /**
     * Check if Firebase SMS is properly configured
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->projectId) && !empty($this->webApiKey);
    }
}
