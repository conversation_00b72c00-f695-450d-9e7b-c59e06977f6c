<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('message');
            $table->timestamp('date')->nullable();
            $table->string('status')->default('pending');
            $table->unsignedBigInteger('from_user_id')->index();
            $table->foreign('from_user_id')->references('id')->on('users')->onDelete('restrict');
            $table->unsignedBigInteger('to_user_id')->index();
            $table->foreign('to_user_id')->references('id')->on('users')->onDelete('restrict');
            $table->unsignedBigInteger('blood_id')->index();
            $table->foreign('blood_id')->references('id')->on('blood')->onDelete('restrict');
            $table->unsignedBigInteger('health_center_id')->index();
            $table->foreign('health_center_id')->references('id')->on('health_center')->onDelete('restrict');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
