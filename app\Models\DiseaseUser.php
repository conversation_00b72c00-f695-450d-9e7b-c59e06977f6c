<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DiseaseUser extends Model
{
    use HasFactory;

    protected $table = 'disease_users';

    protected $fillable = [
        'disease_id',
        'user_id',
    ];

    /**
     * Get the disease associated with this pivot record
     */
    public function disease()
    {
        return $this->belongsTo(Disease::class);
    }

    /**
     * Get the user associated with this pivot record
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
