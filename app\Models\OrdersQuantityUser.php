<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrdersQuantityUser extends Model
{
    use HasFactory;

    protected $table = 'orders_quantity_users';

    protected $fillable = [
        'orders_quantity_id',
        'donor_id',
        'quantity',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the orders quantity associated with this pivot record
     */
    public function ordersQuantity()
    {
        return $this->belongsTo(OrdersQuantity::class);
    }

    /**
     * Get the donor (user) associated with this pivot record
     */
    public function donor()
    {
        return $this->belongsTo(User::class, 'donor_id');
    }
}
