{"info": {"name": "Blood Bank API - Complete Collection", "description": "مجموعة شاملة لجميع APIs مشروع بنك الدم مع Laravel Sanctum Authentication", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{api_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost/blood_bank/public/api", "type": "string"}, {"key": "api_token", "value": "", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('api_token', response.data.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON>م<PERSON> محمد\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+************\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"first_contact_phone\": \"+************\",\n    \"second_contact_phone\": \"+************\",\n    \"gender\": true,\n    \"birthdate\": \"1990-01-01\",\n    \"blood_id\": 1\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('api_token', response.data.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"fcm\": \"firebase_token_here\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "Verify Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+************\",\n    \"verification_code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/verify-account", "host": ["{{base_url}}"], "path": ["verify-account"]}}}, {"name": "Resend Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+************\"\n}"}, "url": {"raw": "{{base_url}}/resend-verification-code", "host": ["{{base_url}}"], "path": ["resend-verification-code"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}], "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"أحم<PERSON> محمد المحدث\",\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}}}]}, {"name": "🩸 Blood Types", "item": [{"name": "Get Blood Types (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/blood-types", "host": ["{{base_url}}"], "path": ["blood-types"]}}}, {"name": "Get Blood Donors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}], "url": {"raw": "{{base_url}}/blood/1/donors", "host": ["{{base_url}}"], "path": ["blood", "1", "donors"]}}}, {"name": "Create Blood Type (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"AB+\",\n    \"status\": true\n}"}, "url": {"raw": "{{base_url}}/blood", "host": ["{{base_url}}"], "path": ["blood"]}}}, {"name": "Update Blood Type (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"AB+ محدث\",\n    \"status\": true\n}"}, "url": {"raw": "{{base_url}}/blood/1", "host": ["{{base_url}}"], "path": ["blood", "1"]}}}]}, {"name": "📋 Orders", "item": [{"name": "Get My Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}], "url": {"raw": "{{base_url}}/orders", "host": ["{{base_url}}"], "path": ["orders"]}}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"message\": \"أحتا<PERSON> متبرع دم عاجل للطوارئ\",\n    \"to_user_id\": 2,\n    \"blood_id\": 1,\n    \"health_center_id\": 1,\n    \"date\": \"2024-01-01 10:00:00\"\n}"}, "url": {"raw": "{{base_url}}/orders", "host": ["{{base_url}}"], "path": ["orders"]}}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"accepted\"\n}"}, "url": {"raw": "{{base_url}}/orders/1", "host": ["{{base_url}}"], "path": ["orders", "1"]}}}, {"name": "Get Sent Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}], "url": {"raw": "{{base_url}}/orders/sent/list", "host": ["{{base_url}}"], "path": ["orders", "sent", "list"]}}}, {"name": "Get Received Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}], "url": {"raw": "{{base_url}}/orders/received/list", "host": ["{{base_url}}"], "path": ["orders", "received", "list"]}}}]}, {"name": "🔔 Notifications", "item": [{"name": "Update FCM Token", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{api_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fcm_token\": \"firebase_token_here\"\n}"}, "url": {"raw": "{{base_url}}/notifications/fcm-token", "host": ["{{base_url}}"], "path": ["notifications", "fcm-token"]}}}, {"name": "Send Test Notification (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2,\n    \"title\": \"إشعار تجريبي\",\n    \"body\": \"هذا إشعار تجريبي\",\n    \"channels\": [\"push\"]\n}"}, "url": {"raw": "{{base_url}}/notifications/test", "host": ["{{base_url}}"], "path": ["notifications", "test"]}}}]}, {"name": "🔥 Firebase SMS", "item": [{"name": "Send Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+************\"\n}"}, "url": {"raw": "{{base_url}}/firebase-sms/send-verification", "host": ["{{base_url}}"], "path": ["firebase-sms", "send-verification"]}}}, {"name": "Get Service Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/firebase-sms/status", "host": ["{{base_url}}"], "path": ["firebase-sms", "status"]}}}]}]}