<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('phone')->unique();
            $table->string('email')->unique()->nullable();
            $table->string('first_contact_phone')->unique()->nullable();
            $table->string('second_contact_phone')->unique()->nullable();
            $table->boolean('is_admin')->default(false);
            $table->boolean('status')->default(false);
            $table->boolean('is_donor')->default(false);
            $table->boolean('gender')->default(true);
            $table->date('birthdate')->nullable();
            $table->string('verification_code')->nullable();
            $table->timestamp('verification_code_expired_at')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->text('fcm')->nullable();
            $table->string('password');
            $table->text('more_info')->nullable();
            $table->unsignedBigInteger('blood_id')->nullable()->index();
            $table->foreign('blood_id')->references('id')->on('blood')->onDelete('restrict');
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
