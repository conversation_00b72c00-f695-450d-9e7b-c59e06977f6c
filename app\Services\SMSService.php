<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Exception;

class SMSService
{
    protected $client;
    protected $provider;

    public function __construct()
    {
        $this->client = new Client();
        $this->provider = config('services.sms.provider', 'twilio');
    }

    /**
     * Send SMS message
     *
     * @param string $to
     * @param string $message
     * @param array $options
     * @return bool
     */
    public function send(string $to, string $message, array $options = []): bool
    {
        try {
            switch ($this->provider) {
                case 'twilio':
                    return $this->sendViaTwilio($to, $message, $options);
                case 'nexmo':
                    return $this->sendViaNexmo($to, $message, $options);
                default:
                    throw new Exception("Unsupported SMS provider: {$this->provider}");
            }
        } catch (Exception $e) {
            Log::error('SMS sending failed: ' . $e->getMessage(), [
                'to' => $to,
                'message' => $message,
                'provider' => $this->provider,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send SMS via Twilio
     *
     * @param string $to
     * @param string $message
     * @param array $options
     * @return bool
     */
    protected function sendViaTwilio(string $to, string $message, array $options = []): bool
    {
        try {
            $sid = config('services.sms.twilio.sid');
            $token = config('services.sms.twilio.token');
            $from = config('services.sms.twilio.from');

            if (!$sid || !$token || !$from) {
                throw new Exception('Twilio credentials not configured');
            }

            $url = "https://api.twilio.com/2010-04-01/Accounts/{$sid}/Messages.json";

            $response = $this->client->post($url, [
                'auth' => [$sid, $token],
                'form_params' => [
                    'From' => $from,
                    'To' => $to,
                    'Body' => $message,
                ]
            ]);

            $statusCode = $response->getStatusCode();
            
            if ($statusCode >= 200 && $statusCode < 300) {
                Log::info('SMS sent successfully via Twilio', [
                    'to' => $to,
                    'from' => $from,
                    'message_length' => strlen($message)
                ]);
                return true;
            }

            throw new Exception("Twilio API returned status code: {$statusCode}");

        } catch (RequestException $e) {
            Log::error('Twilio API request failed: ' . $e->getMessage(), [
                'to' => $to,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send SMS via Nexmo/Vonage
     *
     * @param string $to
     * @param string $message
     * @param array $options
     * @return bool
     */
    protected function sendViaNexmo(string $to, string $message, array $options = []): bool
    {
        try {
            $key = config('services.sms.nexmo.key');
            $secret = config('services.sms.nexmo.secret');
            $from = config('services.sms.nexmo.from');

            if (!$key || !$secret || !$from) {
                throw new Exception('Nexmo credentials not configured');
            }

            $url = 'https://rest.nexmo.com/sms/json';

            $response = $this->client->post($url, [
                'json' => [
                    'api_key' => $key,
                    'api_secret' => $secret,
                    'from' => $from,
                    'to' => $to,
                    'text' => $message,
                    'type' => 'unicode' // Support Arabic text
                ]
            ]);

            $statusCode = $response->getStatusCode();
            $body = json_decode($response->getBody()->getContents(), true);

            if ($statusCode >= 200 && $statusCode < 300 && isset($body['messages'][0]['status']) && $body['messages'][0]['status'] == '0') {
                Log::info('SMS sent successfully via Nexmo', [
                    'to' => $to,
                    'from' => $from,
                    'message_length' => strlen($message)
                ]);
                return true;
            }

            $errorText = $body['messages'][0]['error-text'] ?? 'Unknown error';
            throw new Exception("Nexmo API error: {$errorText}");

        } catch (RequestException $e) {
            Log::error('Nexmo API request failed: ' . $e->getMessage(), [
                'to' => $to,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send bulk SMS messages
     *
     * @param array $recipients
     * @param string $message
     * @param array $options
     * @return array
     */
    public function sendBulk(array $recipients, string $message, array $options = []): array
    {
        $results = [];
        
        foreach ($recipients as $recipient) {
            $results[$recipient] = $this->send($recipient, $message, $options);
        }

        return $results;
    }

    /**
     * Format phone number for international format
     *
     * @param string $phoneNumber
     * @param string $countryCode
     * @return string
     */
    public function formatPhoneNumber(string $phoneNumber, string $countryCode = '+966'): string
    {
        // Remove any non-digit characters
        $phoneNumber = preg_replace('/\D/', '', $phoneNumber);
        
        // If number starts with 0, remove it (Saudi Arabia format)
        if (substr($phoneNumber, 0, 1) === '0') {
            $phoneNumber = substr($phoneNumber, 1);
        }
        
        // If number doesn't start with country code, add it
        if (!str_starts_with($phoneNumber, ltrim($countryCode, '+'))) {
            $phoneNumber = ltrim($countryCode, '+') . $phoneNumber;
        }
        
        return '+' . $phoneNumber;
    }

    /**
     * Validate phone number format
     *
     * @param string $phoneNumber
     * @return bool
     */
    public function validatePhoneNumber(string $phoneNumber): bool
    {
        // Basic validation for international format
        return preg_match('/^\+[1-9]\d{1,14}$/', $phoneNumber);
    }

    /**
     * Get SMS character count and parts
     *
     * @param string $message
     * @return array
     */
    public function getMessageInfo(string $message): array
    {
        $length = mb_strlen($message, 'UTF-8');
        
        // SMS limits
        $singleSmsLimit = 160; // For GSM 7-bit
        $unicodeSmsLimit = 70; // For Unicode (Arabic)
        $multipartSmsLimit = 153; // For GSM 7-bit multipart
        $unicodeMultipartLimit = 67; // For Unicode multipart
        
        // Check if message contains Arabic or other Unicode characters
        $isUnicode = preg_match('/[\x{0600}-\x{06FF}]/u', $message) || $length !== strlen($message);
        
        if ($isUnicode) {
            $singleLimit = $unicodeSmsLimit;
            $multipartLimit = $unicodeMultipartLimit;
        } else {
            $singleLimit = $singleSmsLimit;
            $multipartLimit = $multipartSmsLimit;
        }
        
        if ($length <= $singleLimit) {
            $parts = 1;
        } else {
            $parts = ceil($length / $multipartLimit);
        }
        
        return [
            'length' => $length,
            'parts' => $parts,
            'is_unicode' => $isUnicode,
            'remaining_chars' => $parts === 1 ? ($singleLimit - $length) : ($multipartLimit - ($length % $multipartLimit))
        ];
    }
}
